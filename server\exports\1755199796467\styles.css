
    :root {
      --primary-color: #8b5cf6;
      --secondary-color: #c4b5fd;
      --background-color: #ffffff;
      --text-color: #111827;
      --accent-color: #f97316;
      --font-heading: Inter, sans-serif;
      --font-body: Inter, sans-serif;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: var(--font-body);
      color: var(--text-color);
      background-color: var(--background-color);
      line-height: 1.6;
    }
    
    .dark-mode {
      --background-color: #121212;
      --text-color: #ffffff;
    }
    
    .container {
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
    }
    
    h1, h2, h3, h4, h5, h6 {
      font-family: var(--font-heading);
      margin-bottom: 1rem;
      line-height: 1.2;
    }
    
    h1 {
      font-size: 3rem;
    }
    
    h2 {
      font-size: 2.5rem;
    }
    
    h3 {
      font-size: 1.5rem;
    }
    
    p {
      margin-bottom: 1rem;
    }
    
    a {
      color: var(--primary-color);
      text-decoration: none;
    }
    
    ul {
      list-style: none;
    }
    
    /* Header */
    header {
      background-color: var(--background-color);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      position: sticky;
      top: 0;
      z-index: 100;
      padding: 1rem 0;
    }
    
    header .container {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .logo {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--primary-color);
    }
    
    nav ul {
      display: flex;
    }
    
    nav ul li {
      margin-left: 1.5rem;
    }
    
    nav ul li a {
      transition: color 0.3s ease;
    }
    
    nav ul li a:hover {
      color: var(--primary-color);
    }
    
    /* Hero Section */
    .hero-section {
      background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('https://images.pexels.com/photos/3183150/pexels-photo-3183150.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2');
      background-size: cover;
      background-position: center;
      color: white;
      text-align: center;
      padding: 8rem 0;
    }
    
    .hero-subheadline {
      font-size: 1.5rem;
      margin-bottom: 2rem;
      max-width: 700px;
      margin-left: auto;
      margin-right: auto;
    }
    
    .cta-button {
      display: inline-block;
      background-color: var(--primary-color);
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 4px;
      font-weight: 600;
      transition: background-color 0.3s ease;
    }
    
    .cta-button:hover {
      background-color: var(--secondary-color);
    }
    
    /* About Section */
    .about-section {
      padding: 5rem 0;
      background-color: #f9fafb;
    }
    
    .dark-mode .about-section {
      background-color: #1a1a1a;
    }
    
    .about-content {
      display: flex;
      align-items: center;
      gap: 2rem;
    }
    
    .about-text {
      flex: 1;
    }
    
    .about-image {
      flex: 1;
    }
    
    .about-image img {
      width: 100%;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    
    /* Features Section */
    .features-section {
      padding: 5rem 0;
      text-align: center;
    }
    
    .section-subtitle {
      font-size: 1.25rem;
      margin-bottom: 3rem;
      max-width: 700px;
      margin-left: auto;
      margin-right: auto;
    }
    
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-top: 3rem;
    }
    
    .feature-card {
      background-color: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      transition: transform 0.3s ease;
    }
    
    .dark-mode .feature-card {
      background-color: #1e1e1e;
    }
    
    .feature-card:hover {
      transform: translateY(-5px);
    }
    
    .feature-icon {
      font-size: 2rem;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }
    
    /* Testimonials Section */
    .testimonials-section {
      padding: 5rem 0;
      background-color: #f9fafb;
      text-align: center;
    }
    
    .dark-mode .testimonials-section {
      background-color: #1a1a1a;
    }
    
    .testimonials-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin-top: 3rem;
    }
    
    .testimonial-card {
      background-color: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      text-align: left;
    }
    
    .dark-mode .testimonial-card {
      background-color: #1e1e1e;
    }
    
    .testimonial-quote {
      font-style: italic;
      margin-bottom: 1.5rem;
    }
    
    .author-name {
      font-weight: 600;
      margin-bottom: 0.25rem;
    }
    
    .author-role {
      font-size: 0.875rem;
      color: #6b7280;
    }
    
    .dark-mode .author-role {
      color: #9ca3af;
    }
    
    /* CTA Section */
    .cta-section {
      background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('https://images.pexels.com/photos/7130560/pexels-photo-7130560.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2');
      background-size: cover;
      background-position: center;
      color: white;
      text-align: center;
      padding: 5rem 0;
    }
    
    .cta-subtitle {
      font-size: 1.25rem;
      margin-bottom: 2rem;
      max-width: 700px;
      margin-left: auto;
      margin-right: auto;
    }
    
    /* Custom Section */
    .custom-section {
      padding: 5rem 0;
    }
    
    .custom-content {
      margin-top: 2rem;
    }
    
    /* Footer */
    footer {
      background-color: var(--background-color);
      padding: 2rem 0;
      text-align: center;
      border-top: 1px solid rgba(0, 0, 0, 0.1);
    }
    
    .dark-mode footer {
      border-top-color: rgba(255, 255, 255, 0.1);
    }
    
    /* Responsive */
    @media (max-width: 768px) {
      h1 {
        font-size: 2.5rem;
      }
      
      h2 {
        font-size: 2rem;
      }
      
      .about-content {
        flex-direction: column;
      }
      
      nav ul {
        display: none;
      }
    }
  