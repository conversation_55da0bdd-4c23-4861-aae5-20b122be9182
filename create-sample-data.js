import mongoose from 'mongoose';
import * as dotenv from 'dotenv';
import Page from './server/models/Page.js';

dotenv.config();

console.log('🔧 CREATING SAMPLE DATA FOR DEMONSTRATION\n');

async function createSampleData() {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      retryWrites: true,
      w: 'majority'
    });
    console.log('✅ Connected to MongoDB Atlas');
    
    // Create 3 sample landing pages
    const samplePages = [
      {
        title: "TechStartup - SaaS Landing Page",
        description: "Modern SaaS landing page for tech startup",
        userId: "demo-user-001",
        formData: {
          businessName: "TechStartup Inc",
          industry: "Software",
          tone: "professional",
          brandColors: {
            primary: "#3B82F6",
            secondary: "#1E40AF"
          },
          keyFeatures: ["AI Analytics", "Real-time Dashboard", "API Integration"],
          targetAudience: "Small to Medium Businesses",
          vision: "Revolutionizing business operations with smart technology"
        },
        theme: {
          colorScheme: "light",
          colors: {
            primary: "#3B82F6",
            secondary: "#1E40AF",
            background: "#FFFFFF",
            text: "#1F2937",
            accent: "#F59E0B"
          },
          fonts: {
            heading: "Poppins",
            body: "Inter"
          }
        },
        sections: [
          {
            id: "hero-tech-1",
            type: "hero",
            title: "Hero Section",
            order: 1,
            content: {
              headline: "Transform Your Business with AI",
              subheadline: "Powerful analytics and automation tools",
              ctaText: "Start Free Trial",
              ctaUrl: "/signup"
            }
          },
          {
            id: "features-tech-1", 
            type: "features",
            title: "Features Section",
            order: 2,
            content: {
              title: "Why Choose TechStartup?",
              items: [
                { title: "AI Analytics", description: "Smart insights powered by machine learning" },
                { title: "Real-time Dashboard", description: "Live data visualization and monitoring" }
              ]
            }
          }
        ],
        status: "published",
        isPublished: true,
        publishedUrl: "https://techstartup.vercel.app",
        shareableUrl: "https://landinggen.app/p/techstartup",
        tags: ["tech", "saas", "b2b", "ai"],
        viewCount: 245,
        exportCount: 12,
        deploymentHistory: [
          {
            deployedAt: new Date(),
            deploymentUrl: "https://techstartup.vercel.app",
            status: "success"
          }
        ]
      },
      {
        title: "GreenEco - Environmental Services",
        description: "Landing page for environmental consulting company",
        userId: "demo-user-002", 
        formData: {
          businessName: "GreenEco Solutions",
          industry: "Environmental Services",
          tone: "friendly",
          brandColors: {
            primary: "#22C55E",
            secondary: "#16A34A"
          },
          keyFeatures: ["Sustainability Audits", "Carbon Footprint Analysis", "Green Consulting"],
          targetAudience: "Corporations and Municipalities",
          vision: "Creating a sustainable future for generations to come"
        },
        theme: {
          colorScheme: "light", 
          colors: {
            primary: "#22C55E",
            secondary: "#16A34A",
            background: "#F9FAFB",
            text: "#111827",
            accent: "#FCD34D"
          },
          fonts: {
            heading: "Montserrat",
            body: "Open Sans"
          }
        },
        sections: [
          {
            id: "hero-green-1",
            type: "hero", 
            title: "Hero Section",
            order: 1,
            content: {
              headline: "Building a Sustainable Tomorrow",
              subheadline: "Expert environmental consulting for your business",
              ctaText: "Get Consultation",
              ctaUrl: "/contact"
            }
          },
          {
            id: "about-green-1",
            type: "about",
            title: "About Section", 
            order: 2,
            content: {
              title: "Our Mission",
              description: "We help organizations reduce their environmental impact while improving their bottom line."
            }
          }
        ],
        status: "draft",
        isPublished: false,
        tags: ["environment", "consulting", "sustainability", "b2b"],
        viewCount: 87,
        exportCount: 3
      },
      {
        title: "FitLife - Personal Training Studio",
        description: "Landing page for personal training and fitness studio",
        userId: "demo-user-003",
        formData: {
          businessName: "FitLife Studio",
          industry: "Health & Fitness",
          tone: "energetic",
          brandColors: {
            primary: "#EF4444",
            secondary: "#DC2626"
          },
          keyFeatures: ["Personal Training", "Group Classes", "Nutrition Coaching"],
          targetAudience: "Health-conscious individuals aged 25-45",
          vision: "Empowering people to achieve their fitness goals and live healthier lives"
        },
        theme: {
          colorScheme: "light",
          colors: {
            primary: "#EF4444",
            secondary: "#DC2626", 
            background: "#FFFFFF",
            text: "#1F2937",
            accent: "#F59E0B"
          },
          fonts: {
            heading: "Oswald",
            body: "Roboto"
          }
        },
        sections: [
          {
            id: "hero-fit-1",
            type: "hero",
            title: "Hero Section",
            order: 1,
            content: {
              headline: "Transform Your Body, Transform Your Life",
              subheadline: "Professional personal training in a supportive environment",
              ctaText: "Book Free Session",
              ctaUrl: "/booking"
            }
          },
          {
            id: "testimonials-fit-1",
            type: "testimonials",
            title: "Success Stories",
            order: 2, 
            content: {
              title: "Real Results from Real People",
              testimonials: [
                {
                  name: "Sarah Johnson",
                  text: "Lost 30 pounds and gained so much confidence!",
                  avatar: "https://images.unsplash.com/sarah.jpg"
                }
              ]
            }
          }
        ],
        status: "published",
        isPublished: true,
        publishedUrl: "https://fitlife-studio.netlify.app",
        tags: ["fitness", "health", "personal-training", "b2c"],
        viewCount: 156,
        exportCount: 8,
        deploymentHistory: [
          {
            deployedAt: new Date(Date.now() - 86400000), // 1 day ago
            deploymentUrl: "https://fitlife-studio.netlify.app",
            status: "success"
          }
        ]
      }
    ];

    console.log('📝 Creating sample landing pages...');
    
    for (let i = 0; i < samplePages.length; i++) {
      const page = new Page(samplePages[i]);
      await page.save();
      console.log(`✅ Created: ${page.title}`);
    }
    
    console.log(`\n🎉 Successfully created ${samplePages.length} sample landing pages!`);
    console.log('\n📊 Now run: node view-data.js to see your stored data');
    
  } catch (error) {
    console.error('❌ Error creating sample data:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔒 MongoDB connection closed');
    process.exit(0);
  }
}

createSampleData();
