@echo off
cls
echo.
echo ====================================================
echo    🚀 LANDING PAGE GENERATOR - STARTUP SCRIPT
echo ====================================================
echo.
echo 📊 MongoDB Atlas Connected: cluster-restro.0bp6k.mongodb.net
echo 💾 Database: landing-page-generator
echo.

echo 🔧 Step 1: Installing dependencies...
call npm install > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)
echo ✅ Dependencies installed

echo.
echo 🖥️  Step 2: Starting Backend Server (Port 5000)...
start "Backend Server" cmd /k "echo Backend Server - MongoDB Connected && node server/index-simple.js"

echo.
echo ⏳ Waiting for backend to initialize...
timeout /t 3 /nobreak > nul

echo.
echo 🌐 Step 3: Starting Frontend Server (Port 5173+)...
start "Frontend Server" cmd /k "echo Frontend Server - React + Vite && npm run dev"

echo.
echo ✅ STARTUP COMPLETE!
echo.
echo 🔗 Your application will open automatically:
echo    Frontend: http://localhost:5173 (or next available port)
echo    Backend:  http://localhost:5000
echo    API:      http://localhost:5000/api/health
echo.
echo 📊 MongoDB Data Viewer: mongodb-viewer.html
echo 🛠️  Command Line Viewer: node view-data.js
echo.
echo ⏸️  To stop servers: Close the terminal windows or press Ctrl+C
echo.
echo 🎉 Happy building! Your Landing Page Generator is ready!
echo.
pause
