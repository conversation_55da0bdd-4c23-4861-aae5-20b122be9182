import express from 'express';
import mongoose from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import Page from '../models/Page.js';

const router = express.Router();

// Mock data for development without MongoDB
let mockPages = [];

// Get all pages with optional filtering, sorting, and pagination
router.get('/', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      status, 
      search, 
      sortBy = 'updatedAt', 
      sortOrder = 'desc',
      userId 
    } = req.query;

    if (process.env.MONGODB_URI && mongoose.connection.readyState === 1) {
      // Build query object
      let query = {};
      
      if (status) {
        query.status = status;
      }
      
      if (userId) {
        query.userId = userId;
      }
      
      if (search) {
        query.$or = [
          { title: { $regex: search, $options: 'i' } },
          { 'formData.businessName': { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      // Calculate pagination
      const skip = (parseInt(page) - 1) * parseInt(limit);
      
      // Build sort object
      const sort = {};
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

      // Execute query with pagination
      const pages = await Page.find(query)
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit));

      // Get total count for pagination
      const totalPages = await Page.countDocuments(query);
      const totalCount = totalPages;
      const currentPage = parseInt(page);
      const hasNextPage = currentPage * parseInt(limit) < totalCount;
      const hasPrevPage = currentPage > 1;

      res.status(200).json({
        success: true,
        data: pages,
        pagination: {
          currentPage,
          totalPages: Math.ceil(totalCount / parseInt(limit)),
          totalCount,
          hasNextPage,
          hasPrevPage,
          limit: parseInt(limit)
        },
        storage: 'mongodb'
      });
    } else {
      // Return mock pages with basic filtering
      let filteredPages = mockPages;
      
      if (status) {
        filteredPages = filteredPages.filter(p => p.status === status);
      }
      
      if (search) {
        filteredPages = filteredPages.filter(p => 
          p.title.toLowerCase().includes(search.toLowerCase()) ||
          p.formData.businessName.toLowerCase().includes(search.toLowerCase())
        );
      }

      // Simple sorting
      filteredPages.sort((a, b) => {
        const aValue = sortBy === 'updatedAt' ? new Date(a.updatedAt) : a[sortBy];
        const bValue = sortBy === 'updatedAt' ? new Date(b.updatedAt) : b[sortBy];
        return sortOrder === 'desc' ? bValue - aValue : aValue - bValue;
      });

      res.status(200).json({
        success: true,
        data: filteredPages,
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalCount: filteredPages.length,
          hasNextPage: false,
          hasPrevPage: false,
          limit: filteredPages.length
        },
        storage: 'memory'
      });
    }
  } catch (error) {
    console.error('Error fetching pages:', error);
    res.status(500).json({ 
      success: false,
      message: 'Error fetching pages', 
      error: error.message,
      storage: mongoose.connection.readyState === 1 ? 'mongodb' : 'memory'
    });
  }
});

// Get a specific page with view tracking
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { incrementView = false } = req.query;
    
    if (process.env.MONGODB_URI && mongoose.connection.readyState === 1) {
      let page;
      
      if (incrementView === 'true') {
        // Increment view count and update last viewed timestamp
        page = await Page.findByIdAndUpdate(
          id, 
          { 
            $inc: { viewCount: 1 },
            lastViewedAt: new Date()
          },
          { new: true }
        );
      } else {
        page = await Page.findById(id);
      }
      
      if (!page) {
        return res.status(404).json({ 
          success: false,
          message: 'Page not found',
          storage: 'mongodb'
        });
      }
      
      res.status(200).json({
        success: true,
        data: page,
        storage: 'mongodb'
      });
    } else {
      // Find in mock pages
      const page = mockPages.find(p => p.id === id);
      
      if (!page) {
        return res.status(404).json({ 
          success: false,
          message: 'Page not found',
          storage: 'memory'
        });
      }
      
      // Simulate view tracking for mock data
      if (incrementView === 'true') {
        page.viewCount = (page.viewCount || 0) + 1;
        page.lastViewedAt = new Date();
      }
      
      res.status(200).json({
        success: true,
        data: page,
        storage: 'memory'
      });
    }
  } catch (error) {
    console.error('Error fetching page:', error);
    res.status(500).json({ 
      success: false,
      message: 'Error fetching page', 
      error: error.message,
      storage: mongoose.connection.readyState === 1 ? 'mongodb' : 'memory'
    });
  }
});

// Create a new page with enhanced data validation
router.post('/', async (req, res) => {
  try {
    const pageData = req.body;
    
    // Validation
    if (!pageData.title || !pageData.formData) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: title and formData',
        storage: mongoose.connection.readyState === 1 ? 'mongodb' : 'memory'
      });
    }
    
    // Ensure required fields with defaults
    const processedPageData = {
      ...pageData,
      id: pageData.id || uuidv4(),
      createdAt: new Date(),
      updatedAt: new Date(),
      status: pageData.status || 'draft',
      isPublished: pageData.isPublished || false,
      publishedUrl: pageData.publishedUrl || '',
      viewCount: 0,
      exportCount: 0,
      tags: pageData.tags || [],
      description: pageData.description || `Landing page for ${pageData.formData.businessName}`,
      deploymentHistory: []
    };
    
    if (process.env.MONGODB_URI && mongoose.connection.readyState === 1) {
      const newPage = new Page(processedPageData);
      const savedPage = await newPage.save();
      
      console.log(`✅ Page saved to MongoDB: ${savedPage.title} (ID: ${savedPage._id})`);
      
      res.status(201).json({
        success: true,
        message: 'Page created successfully',
        data: savedPage,
        storage: 'mongodb'
      });
    } else {
      // Add to mock pages
      mockPages.push(processedPageData);
      
      console.log(`✅ Page saved to memory: ${processedPageData.title} (ID: ${processedPageData.id})`);
      
      res.status(201).json({
        success: true,
        message: 'Page created successfully (in-memory storage)',
        data: processedPageData,
        storage: 'memory'
      });
    }
  } catch (error) {
    console.error('Error creating page:', error);
    
    // Handle MongoDB validation errors
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: validationErrors,
        storage: mongoose.connection.readyState === 1 ? 'mongodb' : 'memory'
      });
    }
    
    res.status(500).json({ 
      success: false,
      message: 'Error creating page', 
      error: error.message,
      storage: mongoose.connection.readyState === 1 ? 'mongodb' : 'memory'
    });
  }
});

// Update a page with comprehensive change tracking
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    // Always update the timestamp
    updateData.updatedAt = new Date();
    
    // Handle publishing status changes
    if (updateData.isPublished !== undefined) {
      updateData.status = updateData.isPublished ? 'published' : 'draft';
    }
    
    if (process.env.MONGODB_URI && mongoose.connection.readyState === 1) {
      const updatedPage = await Page.findByIdAndUpdate(id, updateData, { 
        new: true, // Return the updated document
        runValidators: true // Run schema validations
      });
      
      if (!updatedPage) {
        return res.status(404).json({ 
          success: false,
          message: 'Page not found',
          storage: 'mongodb'
        });
      }
      
      console.log(`✅ Page updated in MongoDB: ${updatedPage.title} (ID: ${updatedPage._id})`);
      
      res.status(200).json({
        success: true,
        message: 'Page updated successfully',
        data: updatedPage,
        storage: 'mongodb'
      });
    } else {
      // Update in mock pages
      const pageIndex = mockPages.findIndex(p => p.id === id);
      
      if (pageIndex === -1) {
        return res.status(404).json({ 
          success: false,
          message: 'Page not found',
          storage: 'memory'
        });
      }
      
      mockPages[pageIndex] = { ...mockPages[pageIndex], ...updateData };
      
      console.log(`✅ Page updated in memory: ${mockPages[pageIndex].title} (ID: ${id})`);
      
      res.status(200).json({
        success: true,
        message: 'Page updated successfully (in-memory storage)',
        data: mockPages[pageIndex],
        storage: 'memory'
      });
    }
  } catch (error) {
    console.error('Error updating page:', error);
    
    // Handle MongoDB validation errors
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: validationErrors,
        storage: mongoose.connection.readyState === 1 ? 'mongodb' : 'memory'
      });
    }
    
    res.status(500).json({ 
      success: false,
      message: 'Error updating page', 
      error: error.message,
      storage: mongoose.connection.readyState === 1 ? 'mongodb' : 'memory'
    });
  }
});

// Delete a page with confirmation and logging
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { confirm } = req.query;
    
    // Safety check - require confirmation for production
    if (process.env.NODE_ENV === 'production' && confirm !== 'true') {
      return res.status(400).json({
        success: false,
        message: 'Deletion requires confirmation. Add ?confirm=true to the request.',
        storage: mongoose.connection.readyState === 1 ? 'mongodb' : 'memory'
      });
    }
    
    if (process.env.MONGODB_URI && mongoose.connection.readyState === 1) {
      const deletedPage = await Page.findByIdAndDelete(id);
      
      if (!deletedPage) {
        return res.status(404).json({ 
          success: false,
          message: 'Page not found',
          storage: 'mongodb'
        });
      }
      
      console.log(`🗑️ Page deleted from MongoDB: ${deletedPage.title} (ID: ${deletedPage._id})`);
      
      res.status(200).json({
        success: true,
        message: 'Page deleted successfully',
        data: {
          id: deletedPage._id,
          title: deletedPage.title,
          deletedAt: new Date()
        },
        storage: 'mongodb'
      });
    } else {
      // Delete from mock pages
      const pageIndex = mockPages.findIndex(p => p.id === id);
      
      if (pageIndex === -1) {
        return res.status(404).json({ 
          success: false,
          message: 'Page not found',
          storage: 'memory'
        });
      }
      
      const deletedPage = mockPages.splice(pageIndex, 1)[0];
      
      console.log(`🗑️ Page deleted from memory: ${deletedPage.title} (ID: ${id})`);
      
      res.status(200).json({
        success: true,
        message: 'Page deleted successfully (from in-memory storage)',
        data: {
          id: deletedPage.id,
          title: deletedPage.title,
          deletedAt: new Date()
        },
        storage: 'memory'
      });
    }
  } catch (error) {
    console.error('Error deleting page:', error);
    res.status(500).json({ 
      success: false,
      message: 'Error deleting page', 
      error: error.message,
      storage: mongoose.connection.readyState === 1 ? 'mongodb' : 'memory'
    });
  }
});

// Publish/Unpublish a page
router.patch('/:id/publish', async (req, res) => {
  try {
    const { id } = req.params;
    const { isPublished, publishedUrl = '' } = req.body;
    
    const updateData = {
      isPublished,
      status: isPublished ? 'published' : 'draft',
      updatedAt: new Date()
    };
    
    if (isPublished && publishedUrl) {
      updateData.publishedUrl = publishedUrl;
      updateData.$push = {
        deploymentHistory: {
          deployedAt: new Date(),
          deploymentUrl: publishedUrl,
          status: 'success'
        }
      };
    }
    
    if (process.env.MONGODB_URI && mongoose.connection.readyState === 1) {
      const page = await Page.findByIdAndUpdate(id, updateData, { new: true });
      
      if (!page) {
        return res.status(404).json({
          success: false,
          message: 'Page not found',
          storage: 'mongodb'
        });
      }
      
      res.status(200).json({
        success: true,
        message: `Page ${isPublished ? 'published' : 'unpublished'} successfully`,
        data: page,
        storage: 'mongodb'
      });
    } else {
      const pageIndex = mockPages.findIndex(p => p.id === id);
      
      if (pageIndex === -1) {
        return res.status(404).json({
          success: false,
          message: 'Page not found',
          storage: 'memory'
        });
      }
      
      Object.assign(mockPages[pageIndex], updateData);
      
      res.status(200).json({
        success: true,
        message: `Page ${isPublished ? 'published' : 'unpublished'} successfully`,
        data: mockPages[pageIndex],
        storage: 'memory'
      });
    }
  } catch (error) {
    console.error('Error updating page publish status:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating page publish status',
      error: error.message,
      storage: mongoose.connection.readyState === 1 ? 'mongodb' : 'memory'
    });
  }
});

// Get page statistics
router.get('/:id/stats', async (req, res) => {
  try {
    const { id } = req.params;
    
    if (process.env.MONGODB_URI && mongoose.connection.readyState === 1) {
      const page = await Page.findById(id);
      
      if (!page) {
        return res.status(404).json({
          success: false,
          message: 'Page not found',
          storage: 'mongodb'
        });
      }
      
      const stats = {
        viewCount: page.viewCount || 0,
        exportCount: page.exportCount || 0,
        lastViewedAt: page.lastViewedAt,
        deploymentHistory: page.deploymentHistory || [],
        createdAt: page.createdAt,
        updatedAt: page.updatedAt,
        daysSinceCreation: Math.floor((new Date() - page.createdAt) / (1000 * 60 * 60 * 24)),
        daysSinceUpdate: Math.floor((new Date() - page.updatedAt) / (1000 * 60 * 60 * 24))
      };
      
      res.status(200).json({
        success: true,
        data: stats,
        storage: 'mongodb'
      });
    } else {
      const page = mockPages.find(p => p.id === id);
      
      if (!page) {
        return res.status(404).json({
          success: false,
          message: 'Page not found',
          storage: 'memory'
        });
      }
      
      const stats = {
        viewCount: page.viewCount || 0,
        exportCount: page.exportCount || 0,
        lastViewedAt: page.lastViewedAt,
        createdAt: page.createdAt,
        updatedAt: page.updatedAt,
        daysSinceCreation: Math.floor((new Date() - new Date(page.createdAt)) / (1000 * 60 * 60 * 24)),
        daysSinceUpdate: Math.floor((new Date() - new Date(page.updatedAt)) / (1000 * 60 * 60 * 24))
      };
      
      res.status(200).json({
        success: true,
        data: stats,
        storage: 'memory'
      });
    }
  } catch (error) {
    console.error('Error fetching page stats:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching page stats',
      error: error.message,
      storage: mongoose.connection.readyState === 1 ? 'mongodb' : 'memory'
    });
  }
});

// Duplicate a page
router.post('/:id/duplicate', async (req, res) => {
  try {
    const { id } = req.params;
    const { title } = req.body;
    
    if (process.env.MONGODB_URI && mongoose.connection.readyState === 1) {
      const originalPage = await Page.findById(id);
      
      if (!originalPage) {
        return res.status(404).json({
          success: false,
          message: 'Original page not found',
          storage: 'mongodb'
        });
      }
      
      const duplicatedPage = new Page({
        ...originalPage.toObject(),
        _id: undefined,
        title: title || `${originalPage.title} (Copy)`,
        createdAt: new Date(),
        updatedAt: new Date(),
        isPublished: false,
        publishedUrl: '',
        status: 'draft',
        viewCount: 0,
        exportCount: 0,
        deploymentHistory: []
      });
      
      const savedPage = await duplicatedPage.save();
      
      res.status(201).json({
        success: true,
        message: 'Page duplicated successfully',
        data: savedPage,
        storage: 'mongodb'
      });
    } else {
      const originalPage = mockPages.find(p => p.id === id);
      
      if (!originalPage) {
        return res.status(404).json({
          success: false,
          message: 'Original page not found',
          storage: 'memory'
        });
      }
      
      const duplicatedPage = {
        ...originalPage,
        id: uuidv4(),
        title: title || `${originalPage.title} (Copy)`,
        createdAt: new Date(),
        updatedAt: new Date(),
        isPublished: false,
        publishedUrl: '',
        status: 'draft',
        viewCount: 0,
        exportCount: 0
      };
      
      mockPages.push(duplicatedPage);
      
      res.status(201).json({
        success: true,
        message: 'Page duplicated successfully',
        data: duplicatedPage,
        storage: 'memory'
      });
    }
  } catch (error) {
    console.error('Error duplicating page:', error);
    res.status(500).json({
      success: false,
      message: 'Error duplicating page',
      error: error.message,
      storage: mongoose.connection.readyState === 1 ? 'mongodb' : 'memory'
    });
  }
});

export default router;