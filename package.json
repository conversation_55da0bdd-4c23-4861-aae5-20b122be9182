{"name": "dynamic-landing-page-generator", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint .", "preview": "vite preview", "server": "node server/index.js", "dev:server": "nodemon server/index.js", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "framer-motion": "^10.16.16", "lucide-react": "^0.344.0", "mongoose": "^8.0.3", "openai": "^4.20.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-grid-layout": "^1.4.2", "react-router-dom": "^6.20.1", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "unsplash-js": "^7.0.18", "uuid": "^9.0.1", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.10.4", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-grid-layout": "^1.3.5", "@types/uuid": "^9.0.7", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "nodemon": "^3.0.2", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vitest": "^1.0.4"}}