// Test MongoDB integration
import axios from 'axios';
import * as dotenv from 'dotenv';

dotenv.config();

const testMongoDBIntegration = async () => {
  console.log('🧪 MONGODB INTEGRATION TEST');
  console.log('=' * 50);
  
  const baseURL = 'http://localhost:5000/api';
  
  try {
    // Test 1: Health Check
    console.log('\n📍 TEST 1: Server Health Check');
    console.log('-' * 30);
    
    const healthResponse = await axios.get(`${baseURL}/health`);
    const health = healthResponse.data;
    
    console.log('✅ Server Status:', health.status);
    console.log('🗄️ Storage Mode:', health.storage_mode);
    console.log('🔌 MongoDB Status:', health.database?.mongodb?.status || 'unknown');
    console.log('💾 Database Name:', health.database?.mongodb?.info?.database || 'N/A');
    
    // Test 2: Create a Test Page
    console.log('\n📍 TEST 2: Create Landing Page');
    console.log('-' * 30);
    
    const testPageData = {
      title: 'Test MongoDB Integration Page',
      formData: {
        businessName: 'MongoDB Test Co',
        industry: 'technology',
        tone: 'professional',
        brandColors: {
          primary: '#4F46E5',
          secondary: '#7C3AED'
        },
        keyFeatures: ['Database Storage', 'Persistent Data', 'Scalable Architecture'],
        targetAudience: 'Developers and businesses',
        vision: 'Testing MongoDB integration with comprehensive data persistence'
      },
      sections: [
        {
          id: 'hero-1',
          type: 'hero',
          title: 'Hero Section',
          content: {
            headline: 'Welcome to MongoDB Test Page',
            subtitle: 'Testing persistent data storage',
            buttonText: 'Get Started'
          },
          order: 0
        },
        {
          id: 'features-1',
          type: 'features',
          title: 'Features Section',
          content: {
            title: 'Key Features',
            features: [
              {
                title: 'Database Storage',
                description: 'Persistent data storage with MongoDB'
              },
              {
                title: 'Scalable Architecture',
                description: 'Built to scale with your business needs'
              }
            ]
          },
          order: 1
        }
      ],
      theme: {
        colorScheme: 'light',
        colors: {
          primary: '#4F46E5',
          secondary: '#7C3AED',
          background: '#ffffff',
          text: '#111827'
        },
        fonts: {
          heading: 'Inter, sans-serif',
          body: 'Inter, sans-serif'
        }
      },
      status: 'draft',
      description: 'Test page for MongoDB integration validation'
    };
    
    const createResponse = await axios.post(`${baseURL}/pages`, testPageData);
    const createdPage = createResponse.data;
    
    console.log('✅ Page Created:', createdPage.success ? 'SUCCESS' : 'FAILED');
    console.log('💾 Storage Used:', createdPage.storage);
    console.log('🆔 Page ID:', createdPage.data?.id || createdPage.data?._id);
    console.log('📄 Page Title:', createdPage.data?.title);
    
    const pageId = createdPage.data?.id || createdPage.data?._id;
    
    if (!pageId) {
      throw new Error('No page ID returned from creation');
    }
    
    // Test 3: Retrieve the Created Page
    console.log('\n📍 TEST 3: Retrieve Page');
    console.log('-' * 30);
    
    const getResponse = await axios.get(`${baseURL}/pages/${pageId}?incrementView=true`);
    const retrievedPage = getResponse.data;
    
    console.log('✅ Page Retrieved:', retrievedPage.success ? 'SUCCESS' : 'FAILED');
    console.log('👁️ View Count:', retrievedPage.data?.viewCount || 0);
    console.log('📊 Sections Count:', retrievedPage.data?.sections?.length || 0);
    
    // Test 4: Update the Page
    console.log('\n📍 TEST 4: Update Page');
    console.log('-' * 30);
    
    const updateData = {
      title: 'Updated MongoDB Test Page',
      description: 'Updated description for testing',
      status: 'published',
      isPublished: true
    };
    
    const updateResponse = await axios.put(`${baseURL}/pages/${pageId}`, updateData);
    const updatedPage = updateResponse.data;
    
    console.log('✅ Page Updated:', updatedPage.success ? 'SUCCESS' : 'FAILED');
    console.log('📝 New Title:', updatedPage.data?.title);
    console.log('📊 Status:', updatedPage.data?.status);
    
    // Test 5: Get All Pages
    console.log('\n📍 TEST 5: List All Pages');
    console.log('-' * 30);
    
    const listResponse = await axios.get(`${baseURL}/pages`);
    const pagesList = listResponse.data;
    
    console.log('✅ Pages Listed:', pagesList.success ? 'SUCCESS' : 'FAILED');
    console.log('📄 Total Pages:', pagesList.pagination?.totalCount || pagesList.data?.length || 0);
    console.log('💾 Storage Mode:', pagesList.storage);
    
    // Test 6: Duplicate Page
    console.log('\n📍 TEST 6: Duplicate Page');
    console.log('-' * 30);
    
    const duplicateResponse = await axios.post(`${baseURL}/pages/${pageId}/duplicate`, {
      title: 'Duplicated Test Page'
    });
    const duplicatedPage = duplicateResponse.data;
    
    console.log('✅ Page Duplicated:', duplicatedPage.success ? 'SUCCESS' : 'FAILED');
    console.log('🆔 New Page ID:', duplicatedPage.data?.id || duplicatedPage.data?._id);
    
    const duplicatedPageId = duplicatedPage.data?.id || duplicatedPage.data?._id;
    
    // Test 7: Get Page Statistics
    console.log('\n📍 TEST 7: Page Statistics');
    console.log('-' * 30);
    
    const statsResponse = await axios.get(`${baseURL}/pages/${pageId}/stats`);
    const stats = statsResponse.data;
    
    console.log('✅ Stats Retrieved:', stats.success ? 'SUCCESS' : 'FAILED');
    console.log('👁️ View Count:', stats.data?.viewCount || 0);
    console.log('📅 Days Since Creation:', stats.data?.daysSinceCreation || 0);
    console.log('🕒 Last Viewed:', stats.data?.lastViewedAt || 'Never');
    
    // Test 8: Publish/Unpublish Page
    console.log('\n📍 TEST 8: Publish Page');
    console.log('-' * 30);
    
    const publishResponse = await axios.patch(`${baseURL}/pages/${pageId}/publish`, {
      isPublished: true,
      publishedUrl: 'https://test-page.netlify.app'
    });
    const publishedPage = publishResponse.data;
    
    console.log('✅ Page Published:', publishedPage.success ? 'SUCCESS' : 'FAILED');
    console.log('🌐 Published URL:', publishedPage.data?.publishedUrl || 'N/A');
    
    // Test 9: Delete Duplicated Page
    console.log('\n📍 TEST 9: Delete Duplicated Page');
    console.log('-' * 30);
    
    if (duplicatedPageId) {
      const deleteResponse = await axios.delete(`${baseURL}/pages/${duplicatedPageId}?confirm=true`);
      const deletedPage = deleteResponse.data;
      
      console.log('✅ Page Deleted:', deletedPage.success ? 'SUCCESS' : 'FAILED');
      console.log('🗑️ Deleted ID:', deletedPage.data?.id);
    }
    
    // Final Summary
    console.log('\n' + '=' * 50);
    console.log('🎉 MONGODB INTEGRATION TEST COMPLETE');
    console.log('=' * 50);
    
    console.log(`✅ All tests completed successfully!`);
    console.log(`💾 Storage Mode: ${health.storage_mode}`);
    console.log(`🔗 MongoDB Status: ${health.database?.mongodb?.status || 'unknown'}`);
    console.log(`📊 Features Tested:`);
    console.log(`   • Create Page ✅`);
    console.log(`   • Retrieve Page ✅`);
    console.log(`   • Update Page ✅`);
    console.log(`   • List Pages ✅`);
    console.log(`   • Duplicate Page ✅`);
    console.log(`   • Page Statistics ✅`);
    console.log(`   • Publish/Unpublish ✅`);
    console.log(`   • Delete Page ✅`);
    console.log(`   • View Tracking ✅`);
    
  } catch (error) {
    console.error('\n❌ TEST FAILED:', error.message);
    
    if (error.response) {
      console.error('📊 Status:', error.response.status);
      console.error('💬 Response:', error.response.data);
    }
    
    console.log('\n🔧 TROUBLESHOOTING:');
    console.log('1. Make sure server is running on port 5000');
    console.log('2. Check MongoDB connection in .env file');
    console.log('3. Verify all dependencies are installed');
    console.log('4. Check server logs for detailed error information');
  }
};

testMongoDBIntegration();
