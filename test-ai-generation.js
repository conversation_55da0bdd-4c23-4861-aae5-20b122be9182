import axios from 'axios';

const testAIGeneration = async () => {
  console.log('Testing AI Generation with sample data...');
  
  const testFormData = {
    businessName: 'TechFlow Solutions',
    industry: 'technology',
    tone: 'professional',
    brandColors: {
      primary: '#3b82f6',
      secondary: '#93c5fd'
    },
    keyFeatures: ['Cloud Integration', 'AI-Powered Analytics', 'Enterprise Security'],
    targetAudience: 'Enterprise businesses',
    vision: 'Empowering businesses with cutting-edge technology solutions'
  };

  try {
    console.log('🔄 Sending request to generate API...');
    
    const response = await axios.post('http://localhost:5000/api/generate', {
      formData: testFormData
    });

    if (response.data.success) {
      console.log('✅ Landing page generation successful!');
      console.log('Generated page title:', response.data.page.title);
      console.log('Number of sections:', response.data.page.sections.length);
      console.log('Sections types:', response.data.page.sections.map(s => s.type).join(', '));
      
      // Check if it was generated with AI or mock data
      if (response.data.message) {
        console.log('ℹ️ Generation method:', response.data.message);
      } else {
        console.log('🤖 Generated using AI (Gemini or OpenAI)');
      }
    } else {
      console.log('❌ Generation failed:', response.data.error);
    }
    
  } catch (error) {
    console.log('❌ Request failed:', error.message);
    if (error.response) {
      console.log('Response status:', error.response.status);
      console.log('Response data:', error.response.data);
    }
  }
};

testAIGeneration().catch(console.error);
