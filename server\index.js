import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import mongoose from 'mongoose';
import path from 'path';
import { fileURLToPath } from 'url';
import pagesRoutes from './routes/pages.js';
import generateRoutes from './routes/generate.js';
import exportRoutes from './routes/export.js';
import deployRoutes from './routes/deploy.js';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());

// Basic route for root path
// Health check endpoint
app.get('/api/health', async (req, res) => {
  try {
    // Check MongoDB connection status
    let mongoStatus = 'disconnected';
    let mongoInfo = null;
    
    if (mongoose.connection.readyState === 1) {
      mongoStatus = 'connected';
      mongoInfo = {
        database: mongoose.connection.db?.databaseName,
        host: mongoose.connection.host,
        port: mongoose.connection.port,
        collections: await mongoose.connection.db?.listCollections().toArray()
      };
    } else if (mongoose.connection.readyState === 2) {
      mongoStatus = 'connecting';
    } else if (mongoose.connection.readyState === 3) {
      mongoStatus = 'disconnecting';
    }

    res.json({
      status: 'running',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      endpoints: [
        'GET /api/pages',
        'POST /api/generate',
        'POST /api/export',
        'POST /api/deploy'
      ],
      database: {
        mongodb: {
          status: mongoStatus,
          uri_configured: !!process.env.MONGODB_URI,
          info: mongoInfo
        }
      },
      ai_integrations: {
        openai: process.env.OPENAI_API_KEY ? 'configured' : 'not configured',
        gemini: process.env.GEMINI_API_KEY ? 'configured' : 'not configured'
      },
      storage_mode: mongoStatus === 'connected' ? 'persistent (MongoDB)' : 'in-memory (mock data)'
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message,
      storage_mode: 'in-memory (mock data)'
    });
  }
});

// Routes
app.use('/api/pages', pagesRoutes);
app.use('/api/generate', generateRoutes);
app.use('/api/export', exportRoutes);
app.use('/api/deploy', deployRoutes);

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../dist')));

  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../dist', 'index.html'));
  });
}

// Database connection
const connectDB = async () => {
  try {
    if (process.env.MONGODB_URI) {
      console.log('🔌 Attempting to connect to MongoDB...');
      
      // MongoDB connection options
      const options = {
        serverSelectionTimeoutMS: 10000, // 10 second timeout
        socketTimeoutMS: 45000, // 45 second socket timeout
        maxPoolSize: 10, // Maintain up to 10 socket connections
        minPoolSize: 5, // Maintain a minimum of 5 socket connections
      };

      await mongoose.connect(process.env.MONGODB_URI, options);
      
      // Connection event handlers
      mongoose.connection.on('connected', () => {
        console.log('✅ MongoDB connected successfully');
        console.log(`📊 Database: ${mongoose.connection.db.databaseName}`);
        console.log(`🌐 Host: ${mongoose.connection.host}:${mongoose.connection.port}`);
      });

      mongoose.connection.on('error', (err) => {
        console.error('❌ MongoDB connection error:', err);
      });

      mongoose.connection.on('disconnected', () => {
        console.log('⚠️ MongoDB disconnected');
      });

      // Graceful shutdown
      process.on('SIGINT', async () => {
        try {
          await mongoose.connection.close();
          console.log('🔒 MongoDB connection closed through app termination');
          process.exit(0);
        } catch (error) {
          console.error('❌ Error closing MongoDB connection:', error);
          process.exit(1);
        }
      });

    } else {
      console.log('⚠️ No MongoDB URI provided, running with in-memory storage');
      console.log('💡 Add MONGODB_URI to .env file for persistent storage');
    }
  } catch (error) {
    console.error('❌ MongoDB connection error:', error.message);
    console.log('🔄 Application will continue with mock data storage');
    // Don't exit - let app continue with mock data
  }
};

// Start server
const startServer = async () => {
  try {
    // Connect to MongoDB if URI is provided
    if (process.env.MONGODB_URI) {
      await connectDB();
    }

    app.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
    });
  } catch (error) {
    console.error('Error starting server:', error);
  }
};

startServer();