import OpenAI from 'openai';
import * as dotenv from 'dotenv';

dotenv.config();

const testOpenAI = async () => {
  console.log('Testing OpenAI API Key...');
  
  // Check if API key is configured
  if (!process.env.OPENAI_API_KEY) {
    console.log('❌ OpenAI API key is not configured in .env file');
    return;
  }
  
  console.log('✅ OpenAI API key found in environment');
  console.log('Key starts with:', process.env.OPENAI_API_KEY.substring(0, 20) + '...');
  
  // Initialize OpenAI client
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });
  
  try {
    // Test with a simple completion
    console.log('🔄 Testing API connection...');
    
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "user",
          content: "Hello! Please respond with just 'API Test Successful' if you can read this."
        }
      ],
      max_tokens: 10,
    });
    
    console.log('✅ OpenAI API is working!');
    console.log('Response:', completion.choices[0].message.content);
    
  } catch (error) {
    console.log('❌ OpenAI API Error:');
    console.log('Error code:', error.code);
    console.log('Error message:', error.message);
    
    if (error.code === 'invalid_api_key') {
      console.log('💡 The API key appears to be invalid. Please check:');
      console.log('   1. The key is correctly copied from OpenAI dashboard');
      console.log('   2. The key has not been revoked');
      console.log('   3. The key has the correct permissions');
    } else if (error.code === 'insufficient_quota') {
      console.log('💡 Your OpenAI account has exceeded the quota or ran out of credits');
    }
  }
};

testOpenAI().catch(console.error);
