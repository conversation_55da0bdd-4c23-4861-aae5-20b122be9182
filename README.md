# 🚀 AI-Powered Landing Page Generator

[![React](https://img.shields.io/badge/React-18.0-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)](https://www.typescriptlang.org/)
[![Vite](https://img.shields.io/badge/Vite-Latest-green.svg)](https://vitejs.dev/)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![Express](https://img.shields.io/badge/Express-4.18-lightgrey.svg)](https://expressjs.com/)
[![MongoDB](https://img.shields.io/badge/MongoDB-Latest-green.svg)](https://mongodb.com/)

**Repository**: [https://github.com/anishgupta6801/PROJECT-LANDING-PAGE-GENERATOR](https://github.com/anishgupta6801/PROJECT-LANDING-PAGE-GENERATOR)

A comprehensive MERN stack application with dual AI integration (Google Gemini + OpenAI) that generates beautiful, customizable landing pages based on detailed business information. Features intelligent fallback systems and robust error handling.

## ✨ Key Features

- 📝 **Multi-step Business Form**: Comprehensive data collection including industry, tone, features, and vision
- 🤖 **Dual AI Integration**: Google Gemini (primary) + OpenAI (fallback) + Mock data (safety net)
- ⚡ **Real-time Preview**: Live landing page preview with `/editor` route
- 🎨 **Theme Customization**: Light/dark mode toggle with brand color integration  
- 📱 **Responsive Design**: Mobile-first design with Tailwind CSS
- 💾 **Export & Deploy**: Static HTML/CSS/JS export with Netlify deployment
- 🛡️ **Robust Error Handling**: Multi-level fallback system prevents failures
- 🎯 **Industry-Specific**: Generates varied content for different business types

## 🛠️ Technology Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for blazing-fast development
- **Tailwind CSS** for styling
- **shadcn/ui** component library
- **Framer Motion** for animations
- **Zustand** for state management

### Backend
- **Node.js** with Express.js
- **MongoDB** with Mongoose ODM
- **CORS** middleware for cross-origin requests
- **dotenv** for environment configuration

### AI Integration
- **Google Gemini API** (Primary AI provider)
- **OpenAI GPT API** (Fallback provider)
- **Mock Data System** (Safety fallback)

## 🚀 Quick Start

### Method 1: Using Batch Files (Recommended)

We've created convenient batch files for Windows users:

```bash
# Start both frontend and backend
double-click run-app.bat

# Or start frontend only
double-click start-dev.bat

# Or start backend only
double-click start-frontend.bat
```

### Method 2: Manual Setup

#### Prerequisites

- **Node.js** v18+ ([Download](https://nodejs.org/))
- **MongoDB** (optional, for data persistence)
- **Git** for cloning

#### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/anishgupta6801/PROJECT-LANDING-PAGE-GENERATOR.git
   cd PROJECT-LANDING-PAGE-GENERATOR
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   
   Create a `.env` file in the root directory:
   ```env
   GEMINI_API_KEY=your_gemini_api_key_here
   OPENAI_API_KEY=your_openai_api_key_here
   MONGODB_URI=mongodb://localhost:27017/landing-page-generator
   PORT=5000
   ```

#### Running the Application

**Option A: Command Prompt (Recommended)**
```cmd
# Terminal 1 - Backend Server
cd "path\to\project"
node server/index.js

# Terminal 2 - Frontend Development Server  
cd "path\to\project"
npm run dev
```

**Option B: PowerShell (May require execution policy changes)**
```powershell
# If you get execution policy errors:
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Then run:
npm run dev        # Frontend
npm run server     # Backend (separate terminal)
```

4. **Access the Application**
   - Frontend: [http://localhost:5173](http://localhost:5173)
   - Backend API: [http://localhost:5000](http://localhost:5000)
   - **Landing Page Editor**: [http://localhost:5173/editor](http://localhost:5173/editor)

## 📁 Project Structure

```
project/
├── 📄 README.md                 # This file
├── 📄 package.json              # Dependencies and scripts
├── 📄 vite.config.ts            # Vite configuration
├── 📄 tailwind.config.js        # Tailwind CSS config
├── 📄 tsconfig.json             # TypeScript configuration
├── 📄 .env                      # Environment variables
├── 🔧 run-app.bat              # Start both frontend & backend
├── 🔧 start-dev.bat            # Start frontend only
├── 🔧 start-frontend.bat       # Alternative startup script
├── 📚 Documentation/
│   ├── API_SETUP_GUIDE.md      # API key setup guide
│   ├── DEPLOYMENT.md           # Deployment instructions
│   ├── TROUBLESHOOTING.md      # Common issues & solutions
│   └── FIX_PROGRAM.md          # Program fixes applied
├── 📁 server/                  # Backend code
│   ├── index.js                # Express server entry point
│   ├── models/                 # MongoDB models
│   │   └── Page.js             # Landing page model
│   └── routes/                 # API endpoints
│       ├── generate.js         # AI content generation
│       ├── pages.js            # Page CRUD operations
│       ├── export.js           # Export functionality
│       └── deploy.js           # Deployment handling
└── 📁 src/                     # Frontend code
    ├── App.tsx                 # Main React component
    ├── main.tsx                # React entry point
    ├── index.css               # Global styles
    ├── components/             # React components
    │   ├── editor/             # Page editor components
    │   │   ├── LandingPagePreview.tsx
    │   │   ├── SectionControls.tsx
    │   │   └── ThemeControls.tsx
    │   ├── form/               # Multi-step form
    │   │   ├── MultiStepForm.tsx
    │   │   ├── BusinessInfoForm.tsx
    │   │   ├── BrandStyleForm.tsx
    │   │   ├── KeyFeaturesForm.tsx
    │   │   └── ReviewForm.tsx
    │   ├── landing/            # Landing page sections
    │   │   ├── HeroSection.tsx
    │   │   ├── AboutSection.tsx
    │   │   ├── FeaturesSection.tsx
    │   │   ├── TestimonialsSection.tsx
    │   │   └── CTASection.tsx
    │   ├── layout/             # Layout components
    │   │   ├── Header.tsx
    │   │   └── Footer.tsx
    │   └── ui/                 # shadcn/ui components
    │       ├── button.tsx
    │       ├── input.tsx
    │       ├── select.tsx
    │       └── tabs.tsx
    ├── pages/                  # Page components
    │   ├── HomePage.tsx        # Landing/welcome page
    │   ├── CreatePage.tsx      # Multi-step form page
    │   ├── EditorPage.tsx      # Live preview & editing
    │   └── DashboardPage.tsx   # Saved pages management
    ├── store/                  # State management
    │   └── landingPageStore.ts # Zustand store
    ├── types/                  # TypeScript definitions
    │   └── index.ts            # Type definitions
    └── lib/                    # Utilities
        └── utils.ts            # Helper functions
```

## 🔧 API Endpoints

### Core Generation API
- `POST /api/generate` - Generate complete landing page with AI
  ```json
  {
    "formData": {
      "businessName": "TechCorp",
      "industry": "technology", 
      "tone": "professional",
      "keyFeatures": ["Feature 1", "Feature 2"],
      "targetAudience": "Businesses",
      "vision": "Transform the industry"
    }
  }
  ```

### Page Management
- `GET /api/pages` - Get all saved landing pages
- `GET /api/pages/:id` - Get specific landing page
- `POST /api/pages` - Save new landing page
- `PUT /api/pages/:id` - Update existing page
- `DELETE /api/pages/:id` - Delete landing page

### Export & Deployment  
- `POST /api/export` - Export page to static files
- `GET /api/export/download/:id` - Download exported ZIP
- `POST /api/deploy` - Deploy to Netlify

## 🤖 AI Integration Details

### Intelligent AI Fallback System

The application uses a sophisticated three-tier AI system:

1. **Primary: Google Gemini API** 
   - Latest Google AI model for creative content generation
   - Handles JSON parsing with markdown cleanup
   - Generates industry-specific, customized content

2. **Secondary: OpenAI GPT API**
   - Fallback when Gemini is unavailable/overloaded
   - Professional content generation
   - Quota-aware with graceful degradation

3. **Safety Net: Mock Data System**
   - Ensures app never fails completely
   - Professional template content
   - Maintains consistent structure

### Content Generation Flow

```mermaid
graph TD
    A[User Submits Form] --> B[Generate API Call]
    B --> C{Gemini API Available?}
    C -->|Yes| D[Generate with Gemini]
    C -->|No/Error| E{OpenAI Available?}
    E -->|Yes| F[Generate with OpenAI] 
    E -->|No/Quota| G[Use Mock Data]
    D --> H[Return Landing Page]
    F --> H
    G --> H
```

## 🎯 Where to Find Your Generated Landing Pages

After generating content through the multi-step form:

1. **Automatic Redirect**: You'll be automatically redirected to `/editor`
2. **Direct Access**: Navigate to [http://localhost:5173/editor](http://localhost:5173/editor)
3. **Dashboard**: View all saved pages at `/dashboard`

The editor provides:
- ✅ **Live Preview** of your generated landing page
- 🎨 **Theme Controls** (light/dark mode toggle)  
- ⚙️ **Section Controls** for customization
- 📤 **Export Options** (HTML/CSS/JS)
- 🚀 **Deploy to Netlify** functionality

## 🚨 Troubleshooting Guide

### Common Issues & Solutions

#### 1. **PowerShell Execution Policy Errors**

**Problem**: `cannot be loaded because running scripts is disabled`

**Solutions**:
```powershell
# Method 1: Change execution policy (Recommended)
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Method 2: Bypass for single session
powershell -ExecutionPolicy Bypass

# Method 3: Use Command Prompt instead
# Use cmd instead of PowerShell for all commands
```

#### 2. **Server Won't Start - Port Issues**

**Problem**: `EADDRINUSE: address already in use :::5000`

**Solutions**:
```bash
# Windows - Kill process on port 5000
netstat -ano | findstr :5000
taskkill /PID <process_id> /F

# Alternative: Use different port
# Edit server/index.js and change port to 5001
```

#### 3. **OpenAI Quota Exceeded**

**Problem**: `429 Too Many Requests` or quota exceeded errors

**Solutions**:
- ✅ **No Action Required**: App automatically falls back to Gemini API
- 🔧 **Check API Keys**: Verify both Gemini and OpenAI keys in `.env`
- 💳 **Upgrade Plan**: Add billing to OpenAI account for higher limits
- 🛡️ **Mock Fallback**: App provides professional content even without API access

#### 4. **Frontend Won't Load**

**Problem**: Browser shows connection refused or blank page

**Solutions**:
```bash
# Check if Vite server is running
npm run dev

# Clear cache and restart
rm -rf node_modules/.vite
npm run dev

# Try different port
npm run dev -- --port 3000
```

#### 5. **Database Connection Issues**  

**Problem**: MongoDB connection errors

**Solutions**:
```bash
# App works without MongoDB (optional feature)
# To use MongoDB:
# 1. Install MongoDB locally
# 2. Start MongoDB service
# 3. Update MONGODB_URI in .env
```

#### 6. **AI Content Not Generating**

**Problem**: Getting template content instead of AI-generated

**Solutions**:
1. **Check API Keys**: Verify Gemini API key in `.env` file
2. **Test API Connection**: 
   ```bash
   node test-gemini-direct.js  # Test Gemini API
   ```
3. **Review Logs**: Check server terminal for API errors
4. **Fallback Working**: Template content means fallback system is functioning

### Performance Issues

#### Slow Loading Times
- **Clear Browser Cache**: Hard refresh (Ctrl+F5)
- **Check Network**: Ensure stable internet for AI API calls
- **Resource Usage**: Close unnecessary browser tabs

#### Memory Issues
- **Restart Servers**: Stop and restart both frontend and backend
- **System Resources**: Ensure sufficient RAM available
- **Node Version**: Use Node.js v18+ for optimal performance

## ❓ FAQ - "Why Is My Program Not Working?"

### Q: "I clicked run-app.bat but nothing happens"
**A**: Right-click the batch file → "Run as Administrator" or use Command Prompt:
```cmd
cd "path\to\your\project"
run-app.bat
```

### Q: "The website loads but I can't generate landing pages"
**A**: Check these in order:
1. Is the backend server running? (Should see "Server running on port 5000")
2. Are your API keys set in the `.env` file?
3. Try generating anyway - the app has fallback mock data

### Q: "I get 'Cannot read properties of undefined' errors"
**A**: This usually indicates:
- Missing environment variables
- Server not fully started
- Try restarting both frontend and backend servers

### Q: "Landing pages look generic/templated"
**A**: This means:
- ✅ **Good News**: Your app is working perfectly!
- 🤖 **AI Status**: Currently using fallback/mock data
- 🔧 **To Fix**: Add valid Gemini API key to `.env` file

### Q: "How do I know if AI is actually working?"
**A**: Look for these signs:
- Business name appears in headlines
- Industry-specific terminology 
- Features match your input exactly
- Content varies between different business types

## 🆘 Emergency Reset Instructions

If everything breaks and you need to start fresh:

```bash
# 1. Stop all processes
# Press Ctrl+C in all terminal windows

# 2. Clean installation
rm -rf node_modules
npm install

# 3. Reset environment
# Delete .env file and recreate with your API keys

# 4. Restart everything
npm run dev        # Terminal 1
node server/index.js  # Terminal 2
```

## 🔐 API Key Setup

### Google Gemini API Key
1. Visit [Google AI Studio](https://aistudio.google.com/)
2. Create new project or select existing
3. Generate API key
4. Add to `.env`: `GEMINI_API_KEY=your_key_here`

### OpenAI API Key (Optional)
1. Visit [OpenAI Platform](https://platform.openai.com/)
2. Create account and add billing
3. Generate API key from dashboard
4. Add to `.env`: `OPENAI_API_KEY=your_key_here`

**Note**: App works with just Gemini key. OpenAI is optional fallback.

## 🎨 Customization Guide

### Adding New Industries
1. Edit the industry dropdown in `BusinessInfoForm.tsx`
2. Update tone options to match industry needs  
3. The AI will automatically adapt content

### Modifying Templates
1. **Mock Data**: Edit `server/routes/generate.js` 
2. **Sections**: Add new components in `src/components/landing/`
3. **Styling**: Customize `tailwind.config.js`

### Brand Colors
- Users can set custom brand colors in the form
- Colors automatically apply to generated content
- Dark/light theme toggle respects brand colors

## 🚀 Deployment

### Frontend (Netlify)
```bash
npm run build
# Upload dist/ folder to Netlify
```

### Backend (Heroku/Railway)
```bash
# Add start script to package.json
"scripts": {
  "start": "node server/index.js"
}
```

### Environment Variables for Production
```env
GEMINI_API_KEY=production_gemini_key
OPENAI_API_KEY=production_openai_key  
MONGODB_URI=mongodb+srv://user:<EMAIL>/dbname
PORT=5000
NODE_ENV=production
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature-name`
3. Commit changes: `git commit -m "Add feature"`
4. Push to branch: `git push origin feature-name`
5. Submit pull request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **React Team** for the amazing framework
- **Google** for Gemini AI API
- **OpenAI** for GPT API access  
- **shadcn** for the beautiful UI components
- **Tailwind CSS** for utility-first styling
- **Vite** for lightning-fast development

---

**📞 Support**: If you encounter any issues, check the [TROUBLESHOOTING.md](TROUBLESHOOTING.md) file or create an issue on GitHub.

**⭐ Star this repo** if it helps you create amazing landing pages!
