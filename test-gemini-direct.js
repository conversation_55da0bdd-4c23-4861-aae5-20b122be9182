import { GoogleGenerativeAI } from '@google/generative-ai';
import * as dotenv from 'dotenv';

dotenv.config();

const testGeminiDirectly = async () => {
  console.log('🔧 Direct Gemini API Test with JSON Parsing Fix...\n');
  
  if (!process.env.GEMINI_API_KEY) {
    console.log('❌ Gemini API key not found');
    return;
  }
  
  console.log('✅ API Key configured');
  
  const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
  const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
  
  const testPrompt = `
Create a hero section for a technology startup called "AI Innovations Inc".
Format as JSON:
{
  "type": "hero",
  "content": {
    "headline": "Revolutionary AI Solutions",
    "subheadline": "Transform your business with cutting-edge AI",
    "ctaText": "Get Started",
    "ctaLink": "#contact"
  }
}

Return only valid JSON, no markdown formatting.`;

  try {
    console.log('🔄 Generating content...');
    
    const result = await model.generateContent(testPrompt);
    const response = await result.response;
    let text = response.text();
    
    console.log('📄 Raw response:');
    console.log(text);
    console.log('─'.repeat(50));
    
    // Apply the same cleaning logic as in our server
    if (text.includes('```json')) {
      text = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
    } else if (text.includes('```')) {
      text = text.replace(/```\n?/g, '').trim();
    }
    
    console.log('🧽 Cleaned response:');
    console.log(text);
    console.log('─'.repeat(50));
    
    // Try to parse
    try {
      const parsed = JSON.parse(text);
      console.log('✅ Successfully parsed JSON:');
      console.log(JSON.stringify(parsed, null, 2));
      
      if (parsed.content && parsed.content.headline) {
        console.log('✅ Content structure is valid');
        console.log(`📝 Headline: "${parsed.content.headline}"`);
        console.log(`🎯 CTA: "${parsed.content.ctaText}"`);
      }
      
    } catch (parseError) {
      console.log('❌ JSON parsing failed:', parseError.message);
    }
    
  } catch (error) {
    console.log('❌ Gemini API Error:', error.message);
    
    if (error.message.includes('overloaded') || error.message.includes('503')) {
      console.log('💡 Gemini is temporarily overloaded - this is normal during peak hours');
    } else if (error.message.includes('quota') || error.message.includes('limit')) {
      console.log('💡 API quota or rate limit reached');
    }
  }
};

testGeminiDirectly().catch(console.error);
