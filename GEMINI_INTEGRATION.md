# Gemini AI Integration

## Overview
The Landing Page Generator now supports **Google Gemini AI** in addition to OpenAI for generating intelligent, contextual landing page content.

## Configuration
Add your Gemini API key to the `.env` file:
```bash
GEMINI_API_KEY=your_gemini_api_key_here
```

Get your API key from: https://aistudio.google.com/app/apikey

## How It Works

### AI Priority System
1. **Gemini AI** (Primary) - Used first if available
2. **OpenAI** (Fallback) - Used if Gemini fails or isn't configured
3. **Mock Data** (Last Resort) - Used if no AI is available

### Features
- **Intelligent Content Generation**: Creates contextual headlines, descriptions, and CTAs
- **Business-Specific Customization**: Tailors content based on industry, tone, and target audience
- **Multiple Section Types**: Supports Hero, About, Features, Testimonials, and CTA sections
- **Fallback System**: Ensures the app always works, even without AI

### API Endpoints
- `POST /api/generate` - Generate complete landing page
- `POST /api/generate/section` - Generate individual sections

### Testing
The integration includes automatic testing for:
- API key validation
- Connection testing
- Error handling and fallbacks

## Benefits of Gemini Integration
- **Free Tier**: Generous free usage limits
- **Fast Response**: Quick content generation
- **High Quality**: Advanced language understanding
- **Reliable**: Robust error handling with OpenAI fallback

## Error Handling
If Gemini AI fails:
1. Automatically tries OpenAI (if configured)
2. Falls back to professional mock data
3. Logs errors for debugging
4. Ensures seamless user experience

Your landing page generator will now create more engaging, contextual content using the latest AI technology!
