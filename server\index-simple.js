import express from 'express';
import cors from 'cors';
import mongoose from 'mongoose';
import * as dotenv from 'dotenv';
import pagesRoutes from './routes/pages.js';
import generateRoutes from './routes/generate.js';
import exportRoutes from './routes/export.js';
import deployRoutes from './routes/deploy.js';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Simple MongoDB connection
const connectDB = async () => {
  try {
    if (mongoose.connection.readyState === 0) {
      console.log('📊 Connecting to MongoDB...');
      await mongoose.connect(process.env.MONGODB_URI, {
        retryWrites: true,
        w: 'majority'
      });
      console.log('✅ MongoDB connected successfully!');
      console.log('📊 Database:', mongoose.connection.db.databaseName);
    } else {
      console.log('✅ MongoDB already connected');
    }
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    // Don't exit, continue with mock data
  }
};

// Health check endpoint
app.get('/api/health', async (req, res) => {
  const dbStatus = mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';
  res.json({
    status: 'Server is running',
    mongodb: dbStatus,
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// Routes
app.use('/api/pages', pagesRoutes);
app.use('/api/generate', generateRoutes);
app.use('/api/export', exportRoutes);
app.use('/api/deploy', deployRoutes);

// Root endpoint
app.get('/', (req, res) => {
  res.json({ message: '🚀 Landing Page Generator API' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
const startServer = async () => {
  try {
    // Connect to MongoDB first
    await connectDB();
    
    // Start the server
    const server = app.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT}`);
      console.log(`🔗 API URL: http://localhost:${PORT}`);
      console.log(`❤️ Health check: http://localhost:${PORT}/api/health`);
      console.log(`✨ Server is ready for testing!`);
    });

    // Keep server alive
    server.keepAliveTimeout = 0;
    server.headersTimeout = 0;
    
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Start the server
startServer();

// Keep the process alive
setInterval(() => {
  // This keeps the event loop active
}, 1000);
