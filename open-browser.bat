@echo off
cls
echo.
echo ====================================================
echo    🌐 LANDING PAGE GENERATOR - BROWSER LAUNCHER
echo ====================================================
echo.

echo 🔍 Checking server status...
echo.

echo 🖥️ Backend Server (MongoDB API):
curl -s http://localhost:5000/api/health | findstr "status" > nul
if %errorlevel% equ 0 (
    echo ✅ Backend running on http://localhost:5000
    echo    - API Health: http://localhost:5000/api/health
    echo    - API Pages: http://localhost:5000/api/pages
) else (
    echo ❌ Backend not responding
    echo    Please run: node server/index-simple.js
)

echo.
echo 🌐 Frontend Server (React App):
curl -s http://localhost:5175 > nul
if %errorlevel% equ 0 (
    echo ✅ Frontend running on http://localhost:5175
) else (
    echo ❌ Frontend not responding
    echo    Please run: npm run dev
)

echo.
echo 🚀 Opening your Landing Page Generator...
echo.

REM Open the main application
start "" "http://localhost:5175"

REM Wait a moment then open the MongoDB viewer
timeout /t 2 /nobreak > nul
echo 📊 Also available: MongoDB Data Viewer
echo    File: mongodb-viewer.html (open manually)

echo.
echo 🎉 Your Landing Page Generator is ready!
echo.
echo 📍 Main Application: http://localhost:5175
echo 🔧 Backend API: http://localhost:5000
echo 📊 Data Viewer: mongodb-viewer.html
echo.
pause
