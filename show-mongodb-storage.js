import mongoose from 'mongoose';
import * as dotenv from 'dotenv';
import Page from './server/models/Page.js';

dotenv.config();

console.log('📊 DEMONSTRATING MONGODB STORAGE - Sample Data\n');

async function showSampleData() {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      retryWrites: true,
      w: 'majority'
    });
    console.log('✅ Connected to MongoDB Atlas');
    console.log('📊 Database:', mongoose.connection.db.databaseName);
    console.log();

    // Create a comprehensive sample landing page
    console.log('📝 CREATING SAMPLE LANDING PAGE DATA...');
    
    const samplePage = new Page({
      title: "EcoTech Solutions - Green Energy Landing Page",
      description: "Modern landing page for renewable energy company",
      userId: "demo-user-123",
      formData: {
        businessName: "EcoTech Solutions",
        industry: "Renewable Energy",
        tone: "professional",
        brandColors: {
          primary: "#22C55E", // Green
          secondary: "#16A34A" // Darker green
        },
        keyFeatures: [
          "Solar Panel Installation",
          "Energy Efficiency Audits", 
          "Smart Grid Integration",
          "24/7 Monitoring System",
          "Government Rebate Assistance"
        ],
        targetAudience: "Homeowners and Small Businesses",
        vision: "Creating a sustainable future through innovative green energy solutions"
      },
      theme: {
        colorScheme: "light",
        colors: {
          primary: "#22C55E",
          secondary: "#16A34A",
          background: "#FFFFFF", 
          text: "#1F2937",
          accent: "#FCD34D" // Yellow accent
        },
        fonts: {
          heading: "Poppins",
          body: "Inter"
        }
      },
      sections: [
        {
          id: "hero-ecotech-1",
          type: "hero",
          title: "Hero Section",
          order: 1,
          content: {
            headline: "Power Your Future with Clean Energy",
            subheadline: "Professional solar installations with 25-year warranty",
            ctaText: "Get Free Quote",
            ctaUrl: "/quote",
            backgroundImage: "https://images.unsplash.com/solar-panels-house.jpg",
            features: ["Free Consultation", "Government Rebates", "25-Year Warranty"],
            statistics: {
              experience: "15+ Years",
              installations: "5,000+",
              savings: "Up to 90%"
            }
          }
        },
        {
          id: "features-ecotech-1",
          type: "features", 
          title: "Our Services",
          order: 2,
          content: {
            title: "Complete Energy Solutions",
            subtitle: "Everything you need for clean, affordable energy",
            items: [
              {
                icon: "solar-panel",
                title: "Solar Panel Installation",
                description: "High-efficiency panels with professional installation and monitoring"
              },
              {
                icon: "energy-audit", 
                title: "Energy Efficiency Audits",
                description: "Comprehensive home energy assessment to maximize savings"
              },
              {
                icon: "smart-grid",
                title: "Smart Grid Integration", 
                description: "Connect to smart grid for optimal energy management"
              },
              {
                icon: "monitoring",
                title: "24/7 System Monitoring",
                description: "Real-time monitoring and maintenance alerts"
              }
            ]
          }
        },
        {
          id: "testimonials-ecotech-1",
          type: "testimonials",
          title: "Customer Success Stories", 
          order: 3,
          content: {
            title: "What Our Customers Say",
            subtitle: "Real stories from satisfied customers",
            testimonials: [
              {
                name: "Michael Chen",
                company: "Homeowner, Sacramento",
                text: "Cut our electricity bill by 85% in the first year. The team was professional and the system works flawlessly!",
                avatar: "https://images.unsplash.com/michael-avatar.jpg",
                rating: 5,
                savings: "$2,400/year"
              },
              {
                name: "Sarah Rodriguez", 
                company: "Small Business Owner",
                text: "The commercial solar installation exceeded our expectations. ROI achieved in just 4 years!",
                avatar: "https://images.unsplash.com/sarah-avatar.jpg", 
                rating: 5,
                savings: "$15,000/year"
              }
            ]
          }
        },
        {
          id: "cta-ecotech-1",
          type: "cta",
          title: "Get Started Today",
          order: 4, 
          content: {
            headline: "Ready to Switch to Solar?",
            subheadline: "Join thousands of satisfied customers saving money with clean energy",
            primaryButton: {
              text: "Get Free Quote",
              url: "/quote",
              style: "primary"
            },
            secondaryButton: {
              text: "Calculate Savings", 
              url: "/calculator",
              style: "secondary"
            },
            urgency: "Limited time: 30% federal tax credit available",
            guarantees: [
              "Free consultation and quote",
              "No upfront costs available", 
              "25-year equipment warranty",
              "100% satisfaction guarantee"
            ]
          }
        }
      ],
      status: "published",
      isPublished: true,
      publishedUrl: "https://ecotech-solutions.vercel.app",
      shareableUrl: "https://landinggen.app/p/ecotech-solutions",
      tags: ["renewable-energy", "solar", "green-tech", "b2c", "lead-gen"],
      viewCount: 0,
      exportCount: 0
    });

    const savedPage = await samplePage.save();
    console.log('✅ Sample page created successfully!');
    console.log();
    
    // Show what gets stored in MongoDB
    console.log('🗄️ WHAT GETS STORED IN MONGODB:');
    console.log('=====================================');
    console.log();
    
    console.log('📄 DOCUMENT ID:', savedPage._id);
    console.log('📝 TITLE:', savedPage.title);
    console.log('👤 USER ID:', savedPage.userId);
    console.log('📅 CREATED:', savedPage.createdAt);
    console.log();
    
    console.log('🏢 BUSINESS INFORMATION:');
    console.log('  Business Name:', savedPage.formData.businessName);
    console.log('  Industry:', savedPage.formData.industry);
    console.log('  Target Audience:', savedPage.formData.targetAudience);
    console.log('  Key Features:', savedPage.formData.keyFeatures.length, 'features stored');
    console.log('  Brand Colors:', savedPage.formData.brandColors);
    console.log();
    
    console.log('🎨 THEME CONFIGURATION:');
    console.log('  Color Scheme:', savedPage.theme.colorScheme);
    console.log('  Primary Color:', savedPage.theme.colors.primary);
    console.log('  Background:', savedPage.theme.colors.background); 
    console.log('  Heading Font:', savedPage.theme.fonts.heading);
    console.log();
    
    console.log('📄 PAGE SECTIONS (' + savedPage.sections.length + ' sections stored):');
    savedPage.sections.forEach((section, index) => {
      console.log(`  ${index + 1}. ${section.type.toUpperCase()} - "${section.title}"`);
      console.log(`     Order: ${section.order}, ID: ${section.id}`);
      if (section.type === 'hero') {
        console.log(`     Headline: "${section.content.headline}"`);
        console.log(`     Features: ${section.content.features?.length || 0} items`);
      } else if (section.type === 'features') {
        console.log(`     Items: ${section.content.items?.length || 0} features`);
      } else if (section.type === 'testimonials') {
        console.log(`     Testimonials: ${section.content.testimonials?.length || 0} reviews`);
      }
    });
    console.log();
    
    console.log('📊 STATUS & PUBLISHING:');
    console.log('  Status:', savedPage.status);
    console.log('  Published:', savedPage.isPublished);
    console.log('  Published URL:', savedPage.publishedUrl);
    console.log('  Shareable URL:', savedPage.shareableUrl);
    console.log();
    
    console.log('🏷️ ORGANIZATION:');
    console.log('  Tags:', savedPage.tags.join(', '));
    console.log('  Description:', savedPage.description);
    console.log();
    
    console.log('📈 ANALYTICS (initialized):');
    console.log('  View Count:', savedPage.viewCount);
    console.log('  Export Count:', savedPage.exportCount);
    console.log('  Deployment History:', savedPage.deploymentHistory.length, 'deployments');
    console.log();
    
    // Show document size
    const docSize = JSON.stringify(savedPage.toObject()).length;
    console.log('💾 STORAGE DETAILS:');
    console.log('  Document Size:', Math.round(docSize / 1024 * 100) / 100, 'KB');
    console.log('  Collection: pages');
    console.log('  Database: landing-page-generator');
    console.log();
    
    // Simulate some updates to show tracking
    console.log('🔄 SIMULATING PAGE UPDATES...');
    
    // Add deployment history
    savedPage.deploymentHistory.push({
      deployedAt: new Date(),
      deploymentUrl: "https://ecotech-solutions.vercel.app",
      status: "success"
    });
    
    // Update analytics
    savedPage.viewCount = 127;
    savedPage.exportCount = 3;
    savedPage.lastViewedAt = new Date();
    
    await savedPage.save();
    console.log('✅ Added deployment history and analytics');
    console.log('  Deployments:', savedPage.deploymentHistory.length);
    console.log('  Views:', savedPage.viewCount);
    console.log('  Exports:', savedPage.exportCount);
    console.log();
    
    console.log('🎯 DATA PERSISTENCE BENEFITS:');
    console.log('  ✅ Complete page data never lost');
    console.log('  ✅ All form inputs preserved');
    console.log('  ✅ Theme settings maintained'); 
    console.log('  ✅ Section content stored');
    console.log('  ✅ Publishing status tracked');
    console.log('  ✅ Analytics accumulated');
    console.log('  ✅ Deployment history maintained');
    console.log('  ✅ Easy to search and filter');
    console.log();
    
    // Clean up
    await Page.deleteOne({ _id: savedPage._id });
    console.log('🧹 Sample data cleaned up');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('🔒 MongoDB connection closed');
    process.exit(0);
  }
}

// Run the demonstration
showSampleData();
