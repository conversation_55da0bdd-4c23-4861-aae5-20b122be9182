import express from 'express';
import cors from 'cors';
import mongoose from 'mongoose';
import * as dotenv from 'dotenv';
import pagesRoutes from './routes/pages.js';
import generateRoutes from './routes/generate.js';
import exportRoutes from './routes/export.js';
import deployRoutes from './routes/deploy.js';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Simple MongoDB connection with proper error handling
const connectDB = async () => {
  try {
    if (process.env.MONGODB_URI) {
      console.log('🔌 Connecting to MongoDB...');
      
      await mongoose.connect(process.env.MONGODB_URI, {
        serverSelectionTimeoutMS: 10000,
        socketTimeoutMS: 45000,
      });
      
      console.log('✅ MongoDB connected successfully!');
      console.log(`📊 Database: ${mongoose.connection.db.databaseName}`);
      
      // Connection event handlers
      mongoose.connection.on('disconnected', () => {
        console.log('⚠️ MongoDB disconnected');
      });
      
      mongoose.connection.on('error', (err) => {
        console.error('❌ MongoDB error:', err.message);
      });
      
    } else {
      console.log('⚠️ No MongoDB URI provided, using in-memory storage');
    }
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    console.log('🔄 Continuing with in-memory storage');
  }
};

// Enhanced health check endpoint
app.get('/api/health', async (req, res) => {
  try {
    const mongoStatus = mongoose.connection.readyState;
    const mongoStatusMap = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting'
    };
    
    let mongoInfo = null;
    if (mongoStatus === 1) {
      try {
        const collections = await mongoose.connection.db.listCollections().toArray();
        mongoInfo = {
          database: mongoose.connection.db.databaseName,
          host: mongoose.connection.host,
          collections: collections.map(c => c.name)
        };
      } catch (err) {
        mongoInfo = { error: 'Could not fetch database info' };
      }
    }

    res.json({
      status: 'running',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      database: {
        mongodb: {
          status: mongoStatusMap[mongoStatus] || 'unknown',
          uri_configured: !!process.env.MONGODB_URI,
          info: mongoInfo
        }
      },
      ai_integrations: {
        openai: process.env.OPENAI_API_KEY ? 'configured' : 'not configured',
        gemini: process.env.GEMINI_API_KEY ? 'configured' : 'not configured'
      },
      storage_mode: mongoStatus === 1 ? 'persistent (MongoDB)' : 'in-memory (mock data)',
      endpoints: [
        'GET /api/health',
        'GET /api/pages',
        'POST /api/pages',
        'GET /api/pages/:id',
        'PUT /api/pages/:id',
        'DELETE /api/pages/:id',
        'PATCH /api/pages/:id/publish',
        'GET /api/pages/:id/stats',
        'POST /api/pages/:id/duplicate',
        'POST /api/generate',
        'POST /api/export',
        'POST /api/deploy'
      ]
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message,
      storage_mode: 'in-memory (mock data)'
    });
  }
});

// Routes
app.use('/api/pages', pagesRoutes);
app.use('/api/generate', generateRoutes);
app.use('/api/export', exportRoutes);
app.use('/api/deploy', deployRoutes);

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Landing Page Generator API',
    version: '1.0.0',
    status: 'running'
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('❌ Server error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    available_routes: ['/api/health', '/api/pages', '/api/generate']
  });
});

// Start server
const startServer = async () => {
  try {
    // Connect to MongoDB first
    await connectDB();
    
    // Start the server
    app.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT}`);
      console.log(`🔗 API URL: http://localhost:${PORT}`);
      console.log(`❤️ Health check: http://localhost:${PORT}/api/health`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Graceful shutdown
process.on('SIGINT', async () => {
  try {
    console.log('\n🔒 Gracefully shutting down...');
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.close();
      console.log('🔒 MongoDB connection closed');
    }
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during shutdown:', error);
    process.exit(1);
  }
});

// Start the server
startServer();
