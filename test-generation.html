<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Landing Page Generation</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        button { background: #3b82f6; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .result { margin-top: 20px; padding: 15px; background: #f3f4f6; border-radius: 5px; }
        .error { background: #fee2e2; color: #dc2626; }
        .success { background: #d1fae5; color: #059669; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Landing Page Generation</h1>
        <p>This will test if the backend API is working correctly.</p>
        
        <button onclick="testGeneration()">Test Generate Landing Page</button>
        
        <div id="result"></div>
    </div>

    <script>
        async function testGeneration() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Testing... Please wait.</p>';
            
            const testFormData = {
                businessName: 'TechCorp Solutions',
                industry: 'technology',
                tone: 'professional',
                brandColors: {
                    primary: '#3b82f6',
                    secondary: '#93c5fd'
                },
                keyFeatures: ['Cloud Computing', 'AI Integration', 'Security'],
                targetAudience: 'Small to medium businesses',
                vision: 'Empowering businesses through technology'
            };
            
            try {
                const response = await fetch('http://localhost:5000/api/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ formData: testFormData })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.success && data.page) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>✅ SUCCESS! Landing Page Generated</h3>
                        <p><strong>Title:</strong> ${data.page.title}</p>
                        <p><strong>Sections:</strong> ${data.page.sections.length}</p>
                        <p><strong>Theme:</strong> ${data.page.theme.colorScheme}</p>
                        <p><strong>Message:</strong> ${data.message || 'Generated successfully'}</p>
                        <details>
                            <summary>View Full Response</summary>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    `;
                } else {
                    throw new Error('Invalid response format');
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ ERROR</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p><strong>Possible causes:</strong></p>
                    <ul>
                        <li>Backend server not running on port 5000</li>
                        <li>CORS issues</li>
                        <li>Network connectivity problems</li>
                    </ul>
                `;
            }
        }
    </script>
</body>
</html>
