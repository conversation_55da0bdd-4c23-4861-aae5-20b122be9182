import { GoogleGenerativeAI } from '@google/generative-ai';
import * as dotenv from 'dotenv';

dotenv.config();

const testGeminiLandingPageGeneration = async () => {
  console.log('🧪 Testing Gemini API for Landing Page Generation...\n');
  
  // Check if API key is configured
  if (!process.env.GEMINI_API_KEY) {
    console.log('❌ Gemini API key is not configured in .env file');
    return;
  }
  
  console.log('✅ Gemini API key found in environment');
  console.log(`Key: ${process.env.GEMINI_API_KEY.substring(0, 15)}...\n`);
  
  // Initialize Gemini client
  const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
  const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
  
  // Test scenarios for different types of businesses
  const testScenarios = [
    {
      name: "Tech Startup",
      formData: {
        businessName: "CloudTech Solutions",
        industry: "technology",
        tone: "innovative",
        brandColors: { primary: "#3b82f6", secondary: "#93c5fd" },
        keyFeatures: ["AI-Powered Analytics", "Cloud Integration", "Real-time Dashboard"],
        targetAudience: "Tech-savvy businesses",
        vision: "Revolutionizing data analytics with AI"
      }
    },
    {
      name: "Healthcare Clinic",
      formData: {
        businessName: "WellCare Medical",
        industry: "healthcare",
        tone: "caring",
        brandColors: { primary: "#10b981", secondary: "#6ee7b7" },
        keyFeatures: ["24/7 Telemedicine", "Online Appointments", "Health Monitoring"],
        targetAudience: "Health-conscious individuals and families",
        vision: "Providing accessible, compassionate healthcare for everyone"
      }
    },
    {
      name: "E-commerce Store",
      formData: {
        businessName: "StyleHub Fashion",
        industry: "retail",
        tone: "trendy",
        brandColors: { primary: "#f59e0b", secondary: "#fbbf24" },
        keyFeatures: ["Latest Fashion Trends", "Fast Shipping", "Easy Returns"],
        targetAudience: "Fashion-forward millennials and Gen Z",
        vision: "Making high-quality fashion accessible to everyone"
      }
    }
  ];

  for (let i = 0; i < testScenarios.length; i++) {
    const scenario = testScenarios[i];
    console.log(`🎯 Test ${i + 1}: ${scenario.name}`);
    console.log(`Business: ${scenario.formData.businessName}`);
    console.log(`Industry: ${scenario.formData.industry}`);
    console.log(`Tone: ${scenario.formData.tone}`);
    console.log(`Features: ${scenario.formData.keyFeatures.join(', ')}\n`);

    try {
      // Create a comprehensive prompt for landing page generation
      const prompt = `
        Create a complete landing page for a ${scenario.formData.industry} business called "${scenario.formData.businessName}".
        The tone should be ${scenario.formData.tone}.
        The key features of the business are: ${scenario.formData.keyFeatures.join(', ')}.
        The target audience is: ${scenario.formData.targetAudience}.
        Business vision: ${scenario.formData.vision}.

        Generate the following sections for the landing page:
        1. Hero section with compelling headline, subheadline, and call-to-action text
        2. About section with engaging company description
        3. Features section with descriptions for each key feature
        4. Testimonials section with 3 realistic customer quotes
        5. Call-to-action section

        Format the response as a JSON object that follows this structure:
        {
          "sections": [
            {
              "type": "hero",
              "content": {
                "headline": "Compelling headline here",
                "subheadline": "Supporting subheadline here",
                "ctaText": "Call to action button text",
                "ctaLink": "#contact"
              }
            },
            {
              "type": "about",
              "content": {
                "title": "About Us",
                "content": "Engaging company description here"
              }
            },
            {
              "type": "features",
              "content": {
                "title": "Our Features",
                "subtitle": "What makes us different",
                "features": [
                  {
                    "title": "Feature name",
                    "description": "Feature description",
                    "icon": "Zap"
                  }
                ]
              }
            },
            {
              "type": "testimonials",
              "content": {
                "title": "What Our Clients Say",
                "testimonials": [
                  {
                    "quote": "Customer testimonial",
                    "author": "Customer name",
                    "role": "Customer role",
                    "company": "Company name"
                  }
                ]
              }
            },
            {
              "type": "cta",
              "content": {
                "title": "Call to action title",
                "subtitle": "Supporting text",
                "buttonText": "Button text",
                "buttonLink": "#contact"
              }
            }
          ]
        }
        
        Return only the JSON object, no additional text. Make sure the content is specific to the ${scenario.formData.industry} industry and uses a ${scenario.formData.tone} tone.`;

      console.log('🔄 Generating landing page content with Gemini AI...');
      
      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      console.log('✅ Generation successful!');
      console.log('📄 Raw response length:', text.length, 'characters');
      
      // Try to parse the JSON response
      try {
        const parsedResponse = JSON.parse(text);
        
        if (parsedResponse.sections && Array.isArray(parsedResponse.sections)) {
          console.log('✅ Valid JSON structure received');
          console.log('📊 Sections generated:', parsedResponse.sections.length);
          
          // Analyze each section
          parsedResponse.sections.forEach((section, index) => {
            console.log(`   ${index + 1}. ${section.type}: ${section.content.title || section.type}`);
            
            // Check content quality for different section types
            switch (section.type) {
              case 'hero':
                if (section.content.headline && section.content.subheadline && section.content.ctaText) {
                  console.log(`      ✅ Hero section complete with headline, subheadline, and CTA`);
                  console.log(`      📝 Headline: "${section.content.headline.substring(0, 50)}..."`);
                } else {
                  console.log(`      ⚠️ Hero section missing required fields`);
                }
                break;
                
              case 'features':
                if (section.content.features && Array.isArray(section.content.features)) {
                  console.log(`      ✅ Features section with ${section.content.features.length} features`);
                  section.content.features.forEach((feature, idx) => {
                    console.log(`         • ${feature.title}: ${feature.description.substring(0, 40)}...`);
                  });
                } else {
                  console.log(`      ⚠️ Features section missing features array`);
                }
                break;
                
              case 'testimonials':
                if (section.content.testimonials && Array.isArray(section.content.testimonials)) {
                  console.log(`      ✅ Testimonials section with ${section.content.testimonials.length} testimonials`);
                  section.content.testimonials.forEach((testimonial, idx) => {
                    console.log(`         • ${testimonial.author} (${testimonial.role}): "${testimonial.quote.substring(0, 40)}..."`);
                  });
                } else {
                  console.log(`      ⚠️ Testimonials section missing testimonials array`);
                }
                break;
            }
          });
          
          console.log('🎉 Content quality check: PASSED\n');
          
        } else {
          console.log('❌ Invalid JSON structure - missing sections array');
          console.log('📄 Response preview:', text.substring(0, 200), '...\n');
        }
        
      } catch (parseError) {
        console.log('❌ Failed to parse JSON response');
        console.log('📄 Response preview:', text.substring(0, 200), '...');
        console.log('🔧 Parse error:', parseError.message, '\n');
      }
      
    } catch (error) {
      console.log('❌ Gemini API Error:');
      console.log('🔧 Error message:', error.message);
      
      if (error.message.includes('overloaded')) {
        console.log('💡 The Gemini API is temporarily overloaded. This is normal during peak usage.');
      } else if (error.message.includes('quota') || error.message.includes('limit')) {
        console.log('💡 API quota or rate limit reached.');
      } else if (error.message.includes('API key')) {
        console.log('💡 There might be an issue with the API key configuration.');
      }
      console.log('');
    }
    
    // Add a small delay between requests to avoid rate limiting
    if (i < testScenarios.length - 1) {
      console.log('⏳ Waiting 2 seconds before next test...\n');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  console.log('🏁 Gemini API testing complete!');
};

testGeminiLandingPageGeneration().catch(console.error);
