<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Landing Pages Database Viewer</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .content {
            padding: 30px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: transform 0.2s;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #64748b;
            font-weight: 500;
        }
        
        .page-grid {
            display: grid;
            gap: 25px;
        }
        
        .page-card {
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 25px;
            background: #fafbfc;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .page-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            border-color: #667eea;
        }
        
        .page-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 20px;
        }
        
        .page-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 5px;
        }
        
        .page-business {
            color: #64748b;
            font-size: 1.1rem;
        }
        
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-published {
            background: #dcfce7;
            color: #166534;
            border: 2px solid #bbf7d0;
        }
        
        .status-draft {
            background: #fef3c7;
            color: #92400e;
            border: 2px solid #fed7aa;
        }
        
        .page-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .detail-group {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .detail-label {
            font-size: 0.85rem;
            color: #64748b;
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .detail-value {
            font-weight: 600;
            color: #1e293b;
        }
        
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 15px;
        }
        
        .tag {
            background: #e0e7ff;
            color: #3730a3;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .sections {
            margin-top: 15px;
        }
        
        .section-item {
            background: white;
            padding: 10px 15px;
            border-radius: 6px;
            margin-bottom: 8px;
            border-left: 3px solid #667eea;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-info {
            font-size: 0.9rem;
        }
        
        .section-type {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            text-transform: uppercase;
        }
        
        .loading {
            text-align: center;
            padding: 60px;
            color: #64748b;
            font-size: 1.2rem;
        }
        
        .error {
            background: #fef2f2;
            border: 2px solid #fecaca;
            color: #dc2626;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }
        
        @media (max-width: 768px) {
            .header h1 { font-size: 2rem; }
            .page-details { grid-template-columns: 1fr; }
            .stats { grid-template-columns: repeat(2, 1fr); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Landing Pages Database</h1>
            <p>Real-time view of your MongoDB Atlas data</p>
        </div>
        
        <div class="content">
            <div id="loading" class="loading">
                🔄 Loading your landing pages...
            </div>
            
            <div id="error" class="error" style="display: none;">
                ❌ Failed to load data. Make sure your server is running on port 5000.
            </div>
            
            <div id="content" style="display: none;">
                <div class="stats" id="stats"></div>
                <div class="page-grid" id="pages"></div>
            </div>
        </div>
    </div>

    <script>
        async function loadData() {
            try {
                const response = await fetch('http://localhost:5000/api/pages?limit=100');
                if (!response.ok) throw new Error('Server not responding');
                
                const data = await response.json();
                displayStats(data);
                displayPages(data.pages || []);
                
                document.getElementById('loading').style.display = 'none';
                document.getElementById('content').style.display = 'block';
                
            } catch (error) {
                console.error('Error loading data:', error);
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
                
                // Show fallback message
                document.getElementById('error').innerHTML = `
                    ❌ <strong>Cannot connect to server</strong><br>
                    <small>Make sure your Landing Page Generator server is running:<br>
                    <code>node server/index-simple.js</code></small>
                `;
            }
        }
        
        function displayStats(data) {
            const pages = data.pages || [];
            const published = pages.filter(p => p.status === 'published').length;
            const draft = pages.filter(p => p.status === 'draft').length;
            const totalViews = pages.reduce((sum, p) => sum + (p.viewCount || 0), 0);
            const totalExports = pages.reduce((sum, p) => sum + (p.exportCount || 0), 0);
            
            document.getElementById('stats').innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${pages.length}</div>
                    <div class="stat-label">Total Pages</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${published}</div>
                    <div class="stat-label">Published</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${draft}</div>
                    <div class="stat-label">Drafts</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${totalViews.toLocaleString()}</div>
                    <div class="stat-label">Total Views</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${totalExports}</div>
                    <div class="stat-label">Exports</div>
                </div>
            `;
        }
        
        function displayPages(pages) {
            if (pages.length === 0) {
                document.getElementById('pages').innerHTML = `
                    <div class="error">
                        📭 No landing pages found<br>
                        <small>Create your first landing page to see data here</small>
                    </div>
                `;
                return;
            }
            
            document.getElementById('pages').innerHTML = pages.map(page => `
                <div class="page-card">
                    <div class="page-header">
                        <div>
                            <div class="page-title">${page.title}</div>
                            <div class="page-business">${page.formData?.businessName || 'Unknown Business'}</div>
                        </div>
                        <div class="status-badge status-${page.status}">
                            ${page.status}
                        </div>
                    </div>
                    
                    <div class="page-details">
                        <div class="detail-group">
                            <div class="detail-label">Industry</div>
                            <div class="detail-value">${page.formData?.industry || 'N/A'}</div>
                        </div>
                        <div class="detail-group">
                            <div class="detail-label">Views</div>
                            <div class="detail-value">${(page.viewCount || 0).toLocaleString()}</div>
                        </div>
                        <div class="detail-group">
                            <div class="detail-label">Exports</div>
                            <div class="detail-value">${page.exportCount || 0}</div>
                        </div>
                        <div class="detail-group">
                            <div class="detail-label">Created</div>
                            <div class="detail-value">${new Date(page.createdAt).toLocaleDateString()}</div>
                        </div>
                    </div>
                    
                    ${page.sections?.length ? `
                        <div class="sections">
                            <div class="detail-label">Sections (${page.sections.length})</div>
                            ${page.sections.map(section => `
                                <div class="section-item">
                                    <div class="section-info">
                                        <strong>${section.title}</strong>
                                    </div>
                                    <div class="section-type">${section.type}</div>
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}
                    
                    ${page.tags?.length ? `
                        <div class="tags">
                            ${page.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                        </div>
                    ` : ''}
                    
                    ${page.publishedUrl ? `
                        <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #e2e8f0;">
                            <div class="detail-label">Published URL</div>
                            <a href="${page.publishedUrl}" target="_blank" style="color: #667eea; text-decoration: none;">
                                ${page.publishedUrl}
                            </a>
                        </div>
                    ` : ''}
                </div>
            `).join('');
        }
        
        // Load data when page loads
        loadData();
        
        // Refresh every 30 seconds
        setInterval(loadData, 30000);
    </script>
</body>
</html>
