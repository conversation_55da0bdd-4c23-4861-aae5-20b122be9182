#!/bin/bash

echo "🚀 Starting Landing Page Generator..."
echo ""

# Change to project directory
cd "e:\MY PROJECTS\REACT+TS+VITE\LANDING PAGE GENERATOR\project"

echo "🔧 Step 1: Starting Backend Server..."
# Start backend in background
nohup node server/index-simple.js > backend.log 2>&1 &
BACKEND_PID=$!
echo "✅ Backend started (PID: $BACKEND_PID)"

# Wait for backend to initialize
sleep 3

echo ""
echo "🌐 Step 2: Starting Frontend Server..."
# Start frontend in background
nohup npm run dev > frontend.log 2>&1 &
FRONTEND_PID=$!
echo "✅ Frontend started (PID: $FRONTEND_PID)"

# Wait for frontend to initialize
sleep 5

echo ""
echo "🔍 Testing servers..."

# Test backend
if curl -s http://localhost:5000/api/health > /dev/null; then
    echo "✅ Backend: http://localhost:5000 - OK"
else
    echo "❌ Backend: Not responding"
fi

# Test frontend (check multiple common ports)
FRONTEND_URL=""
for port in 3000 5173 5174 5175; do
    if curl -s http://localhost:$port > /dev/null; then
        FRONTEND_URL="http://localhost:$port"
        echo "✅ Frontend: $FRONTEND_URL - OK"
        break
    fi
done

if [ -z "$FRONTEND_URL" ]; then
    echo "❌ Frontend: Not responding on common ports"
    echo "   Check frontend.log for details"
fi

echo ""
echo "🎉 Landing Page Generator is running!"
echo ""
echo "📍 URLs:"
echo "   Frontend: $FRONTEND_URL"
echo "   Backend:  http://localhost:5000"
echo "   API:      http://localhost:5000/api/health"
echo ""
echo "📊 View database: node view-data.js"
echo "🛑 Stop servers: kill $BACKEND_PID $FRONTEND_PID"
echo ""

# Save PIDs for cleanup
echo "$BACKEND_PID" > backend.pid
echo "$FRONTEND_PID" > frontend.pid

echo "Press Ctrl+C to stop monitoring..."
trap 'kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; rm -f *.pid *.log; exit' INT

# Keep script running
while true; do
    sleep 10
done
