import axios from 'axios';

const testDetailedContent = async () => {
  console.log('🔍 DETAILED CONTENT ANALYSIS');
  console.log('=' * 50);
  
  const testData = {
    businessName: "FutureTech Innovations",
    industry: "artificial intelligence",
    tone: "cutting-edge",
    brandColors: { primary: "#6366f1", secondary: "#8b5cf6" },
    keyFeatures: ["AI-Powered Analytics", "Predictive Modeling", "Automated Decision Making"],
    targetAudience: "Enterprise clients looking to leverage AI for business transformation",
    vision: "Revolutionizing business operations through advanced artificial intelligence"
  };
  
  try {
    console.log('📊 Testing with AI business data...');
    const response = await axios.post('http://localhost:5000/api/generate', {
      formData: testData
    });
    
    if (response.data.success) {
      const page = response.data.page;
      
      console.log('\n📄 Generated Landing Page Structure:');
      console.log(`Title: ${page.title}`);
      console.log(`Sections: ${page.sections.length}`);
      
      page.sections.forEach((section, idx) => {
        console.log(`\n${idx + 1}. ${section.type.toUpperCase()} SECTION:`);
        
        if (section.type === 'hero') {
          console.log(`   Headline: ${section.content.headline || 'N/A'}`);
          console.log(`   Subtitle: ${section.content.subtitle || 'N/A'}`);
          console.log(`   CTA: ${section.content.cta || 'N/A'}`);
        }
        
        if (section.type === 'features' && section.content.features) {
          console.log(`   Features (${section.content.features.length}):`);
          section.content.features.forEach((feature, i) => {
            console.log(`     ${i + 1}. ${feature.title}`);
            console.log(`        ${feature.description}`);
          });
        }
        
        if (section.type === 'about') {
          console.log(`   Title: ${section.content.title || 'N/A'}`);
          console.log(`   Description: ${section.content.description ? section.content.description.substring(0, 100) + '...' : 'N/A'}`);
        }
        
        if (section.type === 'cta') {
          console.log(`   Title: ${section.content.title || 'N/A'}`);
          console.log(`   Description: ${section.content.description || 'N/A'}`);
          console.log(`   Button: ${section.content.buttonText || 'N/A'}`);
        }
      });
      
      // Check if content is customized or templated
      const heroSection = page.sections.find(s => s.type === 'hero');
      if (heroSection) {
        const headline = heroSection.content.headline;
        const isCustomized = headline.includes('FutureTech') || 
                            headline.includes('AI') || 
                            headline.includes('artificial intelligence') ||
                            !headline.includes('Transform Your');
        
        console.log(`\n✨ Content Analysis:`);
        console.log(`   Customization Level: ${isCustomized ? 'HIGH (AI-generated)' : 'STANDARD (Template)'}`);
        console.log(`   Business Name Integration: ${headline.includes('FutureTech') ? 'YES' : 'NO'}`);
        console.log(`   Industry-Specific Terms: ${headline.toLowerCase().includes('ai') || headline.toLowerCase().includes('artificial') ? 'YES' : 'NO'}`);
      }
      
    } else {
      console.log('❌ Generation failed:', response.data.error);
    }
    
  } catch (error) {
    console.log('❌ Request failed:', error.message);
  }
};

testDetailedContent();
