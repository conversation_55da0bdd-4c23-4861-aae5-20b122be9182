import mongoose from 'mongoose';
import * as dotenv from 'dotenv';

dotenv.config();

// Import our Page model
import Page from './server/models/Page.js';

console.log('🚀 STANDALONE MONGODB INTEGRATION TEST\n');

async function runStandaloneTest() {
  try {
    // Connect to MongoDB
    console.log('📊 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI, {
      retryWrites: true,
      w: 'majority'
    });
    console.log('✅ MongoDB connected successfully!');
    console.log('📊 Database:', mongoose.connection.db.databaseName);
    console.log();

    // Test 1: Create a new page
    console.log('📝 TEST 1: Creating a new landing page...');
    const testPage = new Page({
      title: 'Test Landing Page',
      description: 'A test landing page for MongoDB integration',
      formData: {
        businessName: 'Test Business',
        industry: 'Technology',
        tone: 'professional',
        brandColors: {
          primary: '#3B82F6',
          secondary: '#1E40AF'
        },
        keyFeatures: ['Feature 1', 'Feature 2', 'Feature 3'],
        targetAudience: 'Developers',
        vision: 'To provide amazing solutions'
      },
      theme: {
        colorScheme: 'light',
        colors: {
          primary: '#3B82F6',
          secondary: '#1E40AF',
          background: '#FFFFFF',
          text: '#1F2937',
          accent: '#F59E0B'
        },
        fonts: {
          heading: 'Inter',
          body: 'Inter'
        }
      },
      sections: [
        {
          id: 'hero-1',
          type: 'hero',
          title: 'Hero Section',
          content: {
            headline: 'Welcome to Test Business',
            subheadline: 'We provide amazing solutions'
          },
          order: 1
        },
        {
          id: 'features-1',
          type: 'features',
          title: 'Features Section',
          content: {
            title: 'Our Features',
            items: [
              { title: 'Feature 1', description: 'Description 1' },
              { title: 'Feature 2', description: 'Description 2' }
            ]
          },
          order: 2
        }
      ],
      status: 'draft',
      tags: ['test', 'mongodb', 'integration']
    });

    const savedPage = await testPage.save();
    console.log('✅ Page created successfully!');
    console.log('📄 Page ID:', savedPage._id);
    console.log('📊 Status:', savedPage.status);
    console.log('🏷️ Tags:', savedPage.tags.join(', '));
    console.log();

    // Test 2: Find and update the page
    console.log('🔄 TEST 2: Updating page status to published...');
    const updatedPage = await Page.findByIdAndUpdate(
      savedPage._id,
      { 
        status: 'published',
        publishedAt: new Date(),
        $inc: { viewCount: 5 }
      },
      { new: true }
    );
    console.log('✅ Page updated successfully!');
    console.log('📊 New Status:', updatedPage.status);
    console.log('👁️ View Count:', updatedPage.viewCount);
    console.log('📅 Published At:', updatedPage.publishedAt);
    console.log();

    // Test 3: Add deployment history
    console.log('🚀 TEST 3: Adding deployment history...');
    updatedPage.deploymentHistory.push({
      deployedAt: new Date(),
      deployedBy: 'test-user',
      environment: 'production',
      url: 'https://test-landing-page.vercel.app',
      status: 'success'
    });
    await updatedPage.save();
    console.log('✅ Deployment history added!');
    console.log('🌐 Latest deployment:', updatedPage.deploymentHistory[0].url);
    console.log();

    // Test 4: Query pages with pagination and filtering
    console.log('🔍 TEST 4: Querying pages with filters...');
    const publishedPages = await Page.find({ status: 'published' })
      .select('title status tags viewCount createdAt')
      .sort({ createdAt: -1 })
      .limit(10);
    
    console.log('✅ Query successful!');
    console.log(`📄 Found ${publishedPages.length} published pages`);
    publishedPages.forEach((page, index) => {
      console.log(`  ${index + 1}. ${page.title} (Views: ${page.viewCount})`);
    });
    console.log();

    // Test 5: Aggregate statistics
    console.log('📊 TEST 5: Generating statistics...');
    const stats = await Page.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalViews: { $sum: '$viewCount' },
          totalExports: { $sum: '$exportCount' }
        }
      }
    ]);
    
    console.log('✅ Statistics generated!');
    stats.forEach(stat => {
      console.log(`  📈 ${stat._id}: ${stat.count} pages, ${stat.totalViews} views, ${stat.totalExports} exports`);
    });
    console.log();

    // Test 6: Clean up test data
    console.log('🧹 TEST 6: Cleaning up test data...');
    const deleteResult = await Page.deleteOne({ _id: savedPage._id });
    console.log('✅ Test page deleted!');
    console.log('📊 Deleted count:', deleteResult.deletedCount);
    console.log();

    console.log('🎉 ALL TESTS PASSED! MongoDB integration is working perfectly!');
    console.log('✨ Your Landing Page Generator is ready with full MongoDB support!');
    console.log();
    console.log('🔧 FEATURES VERIFIED:');
    console.log('  ✅ Page creation and storage');
    console.log('  ✅ Status management (draft/published)');
    console.log('  ✅ View tracking and analytics');
    console.log('  ✅ Deployment history tracking');
    console.log('  ✅ Pagination and filtering');
    console.log('  ✅ Statistical aggregation');
    console.log('  ✅ Data cleanup operations');
    
  } catch (error) {
    console.error('❌ TEST FAILED:', error.message);
    if (error.code === 11000) {
      console.error('  🔍 Duplicate key error - page might already exist');
    }
  } finally {
    // Close connection
    await mongoose.connection.close();
    console.log('🔒 MongoDB connection closed');
    process.exit(0);
  }
}

// Run the test
runStandaloneTest();
