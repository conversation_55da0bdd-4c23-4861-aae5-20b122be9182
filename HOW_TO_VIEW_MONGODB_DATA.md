# 🔍 How to View Your MongoDB Data

## 📊 **Method 1: MongoDB Compass (Visual GUI - Recommended)**

### Step 1: Download MongoDB Compass
- Go to: https://www.mongodb.com/products/compass
- Download the free version for Windows
- Install MongoDB Compass

### Step 2: Connect to Your Atlas Cluster
1. Open MongoDB Compass
2. Click "New Connection"
3. Use your connection string:
   ```
   mongodb+srv://landingpage09:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster-Restro
   ```
4. Click "Connect"

### Step 3: Navigate Your Data
- **Database**: `landing-page-generator`
- **Collection**: `pages`
- **Documents**: Each landing page is one document

### What You'll See:
```json
{
  "_id": "64f5a8b9e1234567890abcde",
  "title": "My Landing Page",
  "formData": {
    "businessName": "My Business",
    "industry": "Technology",
    "brandColors": {
      "primary": "#3B82F6"
    }
  },
  "sections": [...],
  "theme": {...},
  "status": "published",
  "viewCount": 25,
  "createdAt": "2024-08-15T10:30:00.000Z"
}
```

---

## 💻 **Method 2: MongoDB Atlas Web Interface**

### Step 1: Go to MongoDB Atlas
- Visit: https://cloud.mongodb.com/
- Log in with your account

### Step 2: Navigate to Your Cluster
1. Click on "Cluster-Restro" (your cluster name)
2. Click "Browse Collections"
3. Select database: `landing-page-generator`
4. Select collection: `pages`

### Step 3: View Documents
- See all your landing pages in a table format
- Click on any document to view full details
- Use filters to search specific pages

---

## 🛠️ **Method 3: Command Line with MongoDB Shell**

### Step 1: Install MongoDB Shell (mongosh)
```bash
# Download from: https://www.mongodb.com/try/download/shell
# Or use npm:
npm install -g mongosh
```

### Step 2: Connect to Your Database
```bash
mongosh "mongodb+srv://landingpage09:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster-Restro"
```

### Step 3: Query Your Data
```javascript
// Switch to your database
use('landing-page-generator')

// View all pages
db.pages.find()

// View pages with pretty formatting
db.pages.find().pretty()

// Count total pages
db.pages.countDocuments()

// Find specific page by title
db.pages.findOne({"title": "My Landing Page"})

// Find published pages only
db.pages.find({"status": "published"})

// Find pages with specific tags
db.pages.find({"tags": "tech"})
```

---

## 🖥️ **Method 4: Custom Admin Dashboard (Code)**

Create a simple admin page to view your data:

```html
<!DOCTYPE html>
<html>
<head>
    <title>Landing Page Admin</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .page { border: 1px solid #ddd; margin: 10px 0; padding: 15px; }
        .status { padding: 5px 10px; border-radius: 5px; color: white; }
        .published { background: green; }
        .draft { background: orange; }
    </style>
</head>
<body>
    <h1>🗄️ Landing Pages Database</h1>
    <div id="pages"></div>

    <script>
        // Fetch pages from your API
        fetch('/api/pages')
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('pages');
                data.pages.forEach(page => {
                    const div = document.createElement('div');
                    div.className = 'page';
                    div.innerHTML = `
                        <h3>${page.title}</h3>
                        <span class="status ${page.status}">${page.status}</span>
                        <p><strong>Business:</strong> ${page.formData?.businessName || 'N/A'}</p>
                        <p><strong>Industry:</strong> ${page.formData?.industry || 'N/A'}</p>
                        <p><strong>Views:</strong> ${page.viewCount}</p>
                        <p><strong>Created:</strong> ${new Date(page.createdAt).toLocaleDateString()}</p>
                        <p><strong>Tags:</strong> ${page.tags?.join(', ') || 'None'}</p>
                    `;
                    container.appendChild(div);
                });
            });
    </script>
</body>
</html>
```

---

## 🔧 **Method 5: VS Code Extension**

### Install MongoDB Extension for VS Code:
1. Open VS Code Extensions (Ctrl+Shift+X)
2. Search for "MongoDB for VS Code"
3. Install the official MongoDB extension
4. Connect using your connection string
5. Browse your data directly in VS Code

---

## 📊 **Method 6: Create a Quick Data Viewer Script**

```javascript
// data-viewer.js - Run this to see your data
import mongoose from 'mongoose';
import * as dotenv from 'dotenv';
import Page from './server/models/Page.js';

dotenv.config();

async function viewData() {
    await mongoose.connect(process.env.MONGODB_URI);
    
    console.log('📊 LANDING PAGES IN DATABASE:');
    console.log('============================\n');
    
    const pages = await Page.find().select('title status formData.businessName viewCount createdAt tags');
    
    pages.forEach((page, index) => {
        console.log(`${index + 1}. ${page.title}`);
        console.log(`   Business: ${page.formData?.businessName || 'N/A'}`);
        console.log(`   Status: ${page.status}`);
        console.log(`   Views: ${page.viewCount}`);
        console.log(`   Created: ${page.createdAt.toDateString()}`);
        console.log(`   Tags: ${page.tags?.join(', ') || 'None'}`);
        console.log('');
    });
    
    console.log(`Total Pages: ${pages.length}`);
    await mongoose.connection.close();
}

viewData();
```

---

## 🎯 **Quick Summary - Best Options:**

### 🥇 **For Beginners:** MongoDB Compass
- Visual interface
- Easy to use
- No coding required
- Perfect for viewing and exploring data

### 🥈 **For Web Access:** MongoDB Atlas Dashboard  
- Access from anywhere
- Built-in filters and search
- No software to install

### 🥉 **For Developers:** MongoDB Shell (mongosh)
- Powerful querying
- Command-line interface  
- Advanced filtering options

### 🏆 **For Your App:** Custom Admin Dashboard
- Integrate with your existing UI
- Custom filtering and views
- Perfect for end users

Would you like me to help you set up any of these methods?
