import { GoogleGenerativeAI } from '@google/generative-ai';
import * as dotenv from 'dotenv';

dotenv.config();

const testGemini = async () => {
  console.log('Testing Gemini API Key...');
  
  // Check if API key is configured
  if (!process.env.GEMINI_API_KEY) {
    console.log('❌ Gemini API key is not configured in .env file');
    return;
  }
  
  console.log('✅ Gemini API key found in environment');
  console.log('Key starts with:', process.env.GEMINI_API_KEY.substring(0, 15) + '...');
  
  // Initialize Gemini client
  const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
  const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
  
  try {
    // Test with a simple generation
    console.log('🔄 Testing API connection...');
    
    const result = await model.generateContent("Hello! Please respond with just 'API Test Successful' if you can read this.");
    const response = await result.response;
    const text = response.text();
    
    console.log('✅ Gemini API is working!');
    console.log('Response:', text);
    
  } catch (error) {
    console.log('❌ Gemini API Error:');
    console.log('Error message:', error.message);
    
    if (error.message.includes('API key')) {
      console.log('💡 The API key appears to be invalid. Please check:');
      console.log('   1. The key is correctly copied from Google AI Studio');
      console.log('   2. The key has not been revoked');
      console.log('   3. The Gemini API is enabled');
    } else if (error.message.includes('quota') || error.message.includes('limit')) {
      console.log('💡 Your Gemini API has exceeded the quota or rate limit');
    }
  }
};

testGemini().catch(console.error);
