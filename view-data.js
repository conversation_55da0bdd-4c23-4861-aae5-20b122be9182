import mongoose from 'mongoose';
import * as dotenv from 'dotenv';
import Page from './server/models/Page.js';

dotenv.config();

console.log('🔍 MONGODB DATA VIEWER\n');

async function viewStoredData() {
  try {
    console.log('📊 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI, {
      retryWrites: true,
      w: 'majority'
    });
    console.log('✅ Connected to MongoDB Atlas');
    console.log('📊 Database:', mongoose.connection.db.databaseName);
    console.log('📄 Collection: pages\n');

    // Get all pages
    const pages = await Page.find().sort({ createdAt: -1 });
    
    if (pages.length === 0) {
      console.log('📭 No pages found in database.');
      console.log('💡 Create some landing pages first, then run this script again.\n');
    } else {
      console.log(`📋 FOUND ${pages.length} LANDING PAGES:\n`);
      console.log('=' .repeat(80));
      
      pages.forEach((page, index) => {
        console.log(`\n${index + 1}. 📄 ${page.title}`);
        console.log(`   🆔 ID: ${page._id}`);
        console.log(`   🏢 Business: ${page.formData?.businessName || 'N/A'}`);
        console.log(`   🏭 Industry: ${page.formData?.industry || 'N/A'}`);
        console.log(`   📊 Status: ${page.status.toUpperCase()}`);
        console.log(`   👁️  Views: ${page.viewCount}`);
        console.log(`   📤 Exports: ${page.exportCount}`);
        console.log(`   📅 Created: ${page.createdAt.toDateString()}`);
        console.log(`   📅 Updated: ${page.updatedAt.toDateString()}`);
        console.log(`   🏷️  Tags: ${page.tags?.length ? page.tags.join(', ') : 'None'}`);
        
        if (page.sections?.length) {
          console.log(`   📄 Sections: ${page.sections.length} sections`);
          page.sections.forEach((section, i) => {
            console.log(`      ${i + 1}. ${section.type} - "${section.title}"`);
          });
        }
        
        if (page.theme) {
          console.log(`   🎨 Theme: ${page.theme.colorScheme} mode, ${page.theme.fonts?.heading || 'default'} font`);
          console.log(`   🎨 Colors: ${page.theme.colors?.primary || 'N/A'} (primary)`);
        }
        
        if (page.deploymentHistory?.length) {
          console.log(`   🚀 Deployments: ${page.deploymentHistory.length}`);
          page.deploymentHistory.forEach((deployment, i) => {
            console.log(`      ${i + 1}. ${deployment.status} - ${deployment.deploymentUrl || 'N/A'}`);
          });
        }
        
        if (page.publishedUrl) {
          console.log(`   🌐 Published URL: ${page.publishedUrl}`);
        }
        
        if (page.shareableUrl) {
          console.log(`   🔗 Shareable URL: ${page.shareableUrl}`);
        }
        
        console.log(`   💾 Document Size: ~${Math.round(JSON.stringify(page.toObject()).length / 1024 * 100) / 100} KB`);
      });
      
      console.log('\n' + '=' .repeat(80));
      
      // Show statistics
      const stats = await Page.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            totalViews: { $sum: '$viewCount' },
            totalExports: { $sum: '$exportCount' }
          }
        }
      ]);
      
      console.log('\n📊 DATABASE STATISTICS:');
      console.log(`   📄 Total Pages: ${pages.length}`);
      
      stats.forEach(stat => {
        console.log(`   📈 ${stat._id.toUpperCase()}: ${stat.count} pages, ${stat.totalViews} views, ${stat.totalExports} exports`);
      });
      
      const totalSize = pages.reduce((acc, page) => acc + JSON.stringify(page.toObject()).length, 0);
      console.log(`   💾 Total Data Size: ~${Math.round(totalSize / 1024 * 100) / 100} KB`);
      
      // Show collection info
      const collectionStats = await mongoose.connection.db.collection('pages').stats();
      console.log(`   🗄️  Collection Size: ~${Math.round(collectionStats.size / 1024 * 100) / 100} KB`);
      console.log(`   📁 Storage Size: ~${Math.round(collectionStats.storageSize / 1024 * 100) / 100} KB`);
      console.log(`   📊 Indexes: ${collectionStats.nindexes} indexes`);
    }
    
    console.log('\n🔧 OTHER WAYS TO VIEW YOUR DATA:');
    console.log('1. 🖥️  MongoDB Compass: https://www.mongodb.com/products/compass');
    console.log('2. 🌐 MongoDB Atlas Dashboard: https://cloud.mongodb.com/');
    console.log('3. 💻 MongoDB Shell: mongosh "your-connection-string"');
    console.log('4. 📱 VS Code Extension: Search "MongoDB for VS Code"');
    
  } catch (error) {
    console.error('❌ Error viewing data:', error.message);
    
    if (error.name === 'MongoNetworkError') {
      console.log('\n🔧 TROUBLESHOOTING:');
      console.log('  1. Check your internet connection');
      console.log('  2. Verify MongoDB connection string in .env file');
      console.log('  3. Make sure MongoDB Atlas cluster is running');
    }
  } finally {
    await mongoose.connection.close();
    console.log('\n🔒 MongoDB connection closed');
    process.exit(0);
  }
}

// Add command line options
const args = process.argv.slice(2);
if (args.includes('--help')) {
  console.log('🔍 MongoDB Data Viewer - Options:');
  console.log('  node view-data.js          - View all pages');
  console.log('  node view-data.js --help   - Show this help');
  process.exit(0);
}

// Run the viewer
viewStoredData();
