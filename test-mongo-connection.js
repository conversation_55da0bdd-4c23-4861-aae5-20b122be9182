import mongoose from 'mongoose';
import * as dotenv from 'dotenv';

dotenv.config();

const testMongoConnection = async () => {
  console.log('🧪 TESTING MONGODB CONNECTION');
  console.log('================================');
  
  const connectionString = process.env.MONGODB_URI;
  
  if (!connectionString) {
    console.log('❌ No MONGODB_URI found in .env file');
    return;
  }
  
  console.log('🔗 Connection string found');
  console.log('📋 Attempting to connect...');
  
  try {
    await mongoose.connect(connectionString, {
      serverSelectionTimeoutMS: 10000,
    });
    
    console.log('✅ MongoDB connection successful!');
    console.log('📊 Database:', mongoose.connection.db.databaseName);
    console.log('🌐 Host:', mongoose.connection.host);
    console.log('📡 Connection state:', mongoose.connection.readyState);
    
    // Test creating a simple document
    const testSchema = new mongoose.Schema({
      name: String,
      createdAt: { type: Date, default: Date.now }
    });
    
    const TestModel = mongoose.model('Test', testSchema);
    
    const testDoc = new TestModel({ name: 'Connection Test' });
    await testDoc.save();
    
    console.log('✅ Test document created successfully');
    console.log('🆔 Document ID:', testDoc._id);
    
    // Clean up test document
    await TestModel.deleteOne({ _id: testDoc._id });
    console.log('🧹 Test document cleaned up');
    
    await mongoose.connection.close();
    console.log('🔒 Connection closed successfully');
    
  } catch (error) {
    console.log('❌ MongoDB connection failed:');
    console.log('💬 Error:', error.message);
    
    if (error.name === 'MongooseServerSelectionError') {
      console.log('\n🔧 TROUBLESHOOTING TIPS:');
      console.log('1. Check your internet connection');
      console.log('2. Verify the connection string is correct');
      console.log('3. Make sure your IP is whitelisted in MongoDB Atlas');
      console.log('4. Check if your network/firewall blocks MongoDB connections');
    }
  }
  
  console.log('\n🏁 Connection test completed');
};

testMongoConnection();
